"""
test_init.py
"""
import logging
import os
import pytest
from mock import patch
from elipy2 import (
    before_send,
    drop_unwanted_events,
    is_dry_run,
    set_code_area,
    sentry_setup,
    convert_log_level,
    log_installation_source,
)


class TestSetCodeArea(object):
    def test_set_code_area(self):
        event = set_code_area(
            {"tags": {}, "exception": {"values": [{"stacktrace": {"frames": []}}]}}
        )
        assert event
        assert event["tags"]["code_area"] == "elipy"

    def test_handles_exceptions(self):
        event = set_code_area("invalid_event_type")
        assert event

    def test_fbenv_stacktrace(self):
        event = set_code_area(
            {
                "tags": {},
                "exception": {
                    "values": [
                        {"stacktrace": {"frames": [{"abs_path": "bin\\fbenv\\custom_script.py"}]}}
                    ]
                },
            }
        )

        assert event["tags"]["code_area"] == "fbenv"


class TestDropUnwantedEvents(object):
    def test_drop_unwanted_events_drop_event(self):
        event = drop_unwanted_events(
            {
                "tags": {"code_area": "fbenv"},
            }
        )
        assert event is None

    def test_drop_unwanted_events(self):
        event = drop_unwanted_events(
            {
                "tags": {"code_area": "elipy"},
            }
        )
        assert event


class TestBeforeSend(object):
    def test_normal_event(self):
        event = before_send(
            {"tags": {}, "exception": {"values": [{"stacktrace": {"frames": []}}]}}, "testing_event"
        )

        assert event

    def test_fbenv_event_dropped(self):
        event = before_send(
            {
                "tags": {},
                "exception": {
                    "values": [
                        {"stacktrace": {"frames": [{"abs_path": "bin\\fbenv\\custom_script.py"}]}}
                    ]
                },
            },
            "testing_event",
        )

        assert not event


class TestIsDryRun(object):
    def test_is_dry_run(self):
        result = is_dry_run()
        assert result is True


class TestSentrySetup:
    @pytest.mark.parametrize(
        "os_env_patch",
        [
            ({"FB_AUTOBUILD": "YES"}),
            ({"FB_AUTOBUILD": ""}),
        ],
    )
    def test_frostbite_variables(self, os_env_patch):
        with patch.dict(os.environ, os_env_patch):
            sentry_setup()

    @pytest.mark.parametrize(
        "os_env_patch",
        [
            ({"ENABLE_SENTRY": "YES"}),
            ({"ENABLE_SENTRY": ""}),
        ],
    )
    def test_enable_variable(self, os_env_patch):
        with patch.dict(os.environ, os_env_patch):
            sentry_setup()

    def test_ado_tags(self):
        with patch.dict(
            os.environ,
            {
                "BUILD_BUILDURI": "true",
                "ENDPOINT_URL_SYSTEMVSSCONNECTION": "ADO_ENDPOINT_URL_SYSTEMVSSCONNECTION",
                "SYSTEM_TEAMPROJECT": "ADO_SYSTEM_TEAMPROJECT",
                "BUILD_BUILDID": "ADO_BUILD_BUILDID",
                "SYSTEM_TASKINSTANCEID": "ADO_SYSTEM_TASKINSTANCEID",
                "SYSTEM_STAGEID": "ADO_SYSTEM_STAGEID",
                "SYSTEM_JOBID": "ADO_SYSTEM_JOBID",
            },
        ):
            sentry_setup()

    def test_jenkins_tags(self):
        with patch.dict(
            os.environ,
            {
                "JENKINS_URL": "https://jenkins-fake.ea.com",
                "JOB_URL": "JENKINS_JOB_URL",
                "JOB_NAME": "JENKINS_JOB_NAME",
                "BUILD_URL": "JENKINS_BUILD_URL",
            },
        ):
            sentry_setup()


class TestLogging(object):
    def test_str_to_log_level(self):
        assert convert_log_level("DEBUG") == logging.DEBUG
        assert convert_log_level("INFO") == logging.INFO
        assert convert_log_level("WARNING") == logging.WARNING
        assert convert_log_level("ERROR") == logging.ERROR
        assert convert_log_level("CRITICAL") == logging.CRITICAL


@patch("elipy2.LOGGER")
class TestLogInstallationSource:
    def test_log_installation_source_af2(self, mock_logger):
        with patch.dict(os.environ, {"AF2_USER": "user", "AF2_TOKEN": "token"}, clear=True):
            log_installation_source()
            mock_logger.info.assert_called_once_with("Elipy was installed from AF2.")

    def test_log_installation_source_af1_no_user(self, mock_logger):
        with patch.dict(os.environ, {"AF2_TOKEN": "token"}, clear=True):
            log_installation_source()
            mock_logger.info.assert_called_once_with("Elipy was installed from AF1.")

    def test_log_installation_source_af1_no_token(self, mock_logger):
        with patch.dict(os.environ, {"AF2_USER": "user"}, clear=True):
            log_installation_source()
            mock_logger.info.assert_called_once_with("Elipy was installed from AF1.")

    def test_log_installation_source_af1_none_set(self, mock_logger):
        with patch.dict(os.environ, {}, clear=True):
            log_installation_source()
            mock_logger.info.assert_called_once_with("Elipy was installed from AF1.")
