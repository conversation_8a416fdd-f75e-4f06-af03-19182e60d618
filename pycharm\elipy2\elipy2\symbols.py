"""
symbols.py

Module that handles symbol actions
"""

# pylint: disable=too-many-lines

import configparser
import os
import retry
import shutil
import tempfile
import time
from typing import Iterable, List, Union
from elipy2 import (
    build_metadata_utils,
    core,
    filer_paths,
    frostbite_core,
    local_paths,
    LOGGER,
    p4,
    SETTINGS,
)
from elipy2.code import CodeUtils
from elipy2.exceptions import BrokenSymbol, CoreException, ELIPYException
from elipy2.frostbite import fbenv_layer, package_utils, sdk_utils
from elipy2.telemetry import collect_metrics


class SymbolsUtils:
    """
    Utils class for symbols
    """

    PLAYSTATION_SOURCE_INDEXING_FILE = "source-info.xml"
    SYMBOL_STORE_LOG = "symstore.log"
    symbol_store_path = os.path.join(
        SETTINGS.get("symbol_stores_share", default=SETTINGS.get("build_share")),
        SETTINGS.get(key="symbols_stores_folder", default="symbol_services"),
        "{}" + SETTINGS.get(key="symbol_stores_suffix", default=""),
    )

    def __init__(self):
        if core.use_bilbo():
            self.bilbo = build_metadata_utils.setup_metadata_manager()

    def _playstation_symbol_store_upload(
        self,
        binaries: Union[Iterable[str], str],
        symbol_store: str,
        platform: str,
        compress: bool = True,
        log_file: bool = False,
        force: bool = False,
        require_source_indexing: bool = True,
    ):
        try:
            os.path.exists(binaries)
            recursive = os.path.isdir(binaries)
            binaries = [binaries]
        except TypeError:
            recursive = False

        # Check if we should ignore PDB errors
        ignore_pdb_errors = SETTINGS.get("ignore_pdb_errors", default="false").lower() == "true"
        if ignore_pdb_errors:
            LOGGER.info("PDB errors will be ignored during PlayStation symbol store upload")

        for binary in binaries:
            folder = binary if recursive else os.path.dirname(binary)
            source_indexing_file_path = os.path.join(folder, self.PLAYSTATION_SOURCE_INDEXING_FILE)
            source_indexed = os.path.exists(source_indexing_file_path)

            if require_source_indexing and not source_indexed:
                raise BrokenSymbol(f"Source indexing file {source_indexing_file_path} is missing.")

            command = (
                ["index", "/custom-index", source_indexing_file_path] if source_indexed else ["add"]
            )
            command += ["/compress"] if compress else []
            command += ["/d", os.path.join(folder, self.SYMBOL_STORE_LOG)] if log_file else []
            command += ["/force"] if force else []
            command += ["/r"] if recursive else []
            _, output, _ = _run_playstation_symupload(
                platform, command + ["/f", binary, "/s", symbol_store, "/o"]
            )

            try:
                self._verify_playstation_symbol_store_upload(output, compress, source_indexed)
            except BrokenSymbol as err:
                # If we're ignoring PDB errors and this is a PDB error, continue
                if ignore_pdb_errors and ".pdb" in str(err):
                    LOGGER.warning("Ignoring PDB error: %s", str(err))
                else:
                    raise

    def _verify_playstation_symbol_store_upload(
        self, command_output: Iterable[str], compress: bool, source_indexed: bool
    ):
        required_files = ["symbols"]

        if source_indexed:
            required_files.insert(0, self.PLAYSTATION_SOURCE_INDEXING_FILE)

        if compress:
            required_files = [f[0:-1] + "_" for f in required_files]

        required_files.append("manifest.txt")

        # Check if we should ignore PDB errors
        ignore_pdb_errors = SETTINGS.get("ignore_pdb_errors", default="false").lower() == "true"

        # Check for PDB errors in the output
        for line in command_output:
            # Check for various PDB error patterns
            is_failed_copy_pdb = "Failed to copy" in line and ".pdb" in line
            is_symstore_error_pdb = "SYMSTORE ERROR" in line and ".pdb" in line
            is_symstore_error_count = (
                "SYMSTORE: Number of errors" in line and line.strip().endswith("1")
            )

            if is_failed_copy_pdb or is_symstore_error_pdb or is_symstore_error_count:
                if ignore_pdb_errors:
                    LOGGER.warning("PDB error detected but will be ignored: %s", line)
                else:
                    raise BrokenSymbol(f"PDB error: {line}")

        for line in command_output:
            if line.startswith("Copied "):
                path = line.rsplit(" ", maxsplit=1)[-1]
                path = path[0 : path.rfind(os.sep)]
                for required_file in required_files:
                    if not os.path.exists(os.path.join(path, required_file)):
                        # If this is a PDB file and we're ignoring PDB errors, continue
                        if ignore_pdb_errors and ".pdb" in required_file:
                            LOGGER.warning(
                                "Ignoring missing PDB file: %s in %s", required_file, path
                            )
                        else:
                            raise BrokenSymbol(f"{required_file} is missing in {path}")

    @collect_metrics()
    def backup_symbols(
        self,
        source=None,
        destination=None,
        branch=None,
        changelist=None,
        platform=None,
        config=None,
        included_extensions=None,
    ):
        """
        Copies symbol files
        """

        def get_source(platform, config):
            """
            Helper function to create path to build source if no specific source is passed.
            """
            if platform is None or config is None:
                raise ELIPYException(
                    "Cannot back up symbols without a platform, config, source and destination."
                )
            source = local_paths.get_local_build_path(platform, config)

            if not os.path.exists(source):
                LOGGER.error("Source {0} does not exits".format(source), exc_info=True)
                raise ELIPYException("Invalid source {} when backing up symbols.".format(source))

            return source

        use_fb_env = frostbite_core.minimum_fb_version(year=2019, version_nr=1)
        if platform and platform.lower() == "tool" and not use_fb_env:
            platforms = ["pipeline", "frosted", "win64-dll"]
        else:
            platforms = [platform]

        for plat in platforms:
            if source is None:
                _source = get_source(plat, config)
            else:
                _source = source

            if destination is None:
                if branch is None or changelist is None or plat is None or config is None:
                    raise ELIPYException(
                        "Cannot back up symbols without a platform, config, source and destination"
                    )
                _destination = filer_paths.get_symbol_path(branch, changelist, plat, config)
            else:
                _destination = destination

            if not os.path.exists(_destination):
                LOGGER.info("Creating directory {0}".format(_destination))
                os.makedirs(_destination)

            # Copy single file
            if os.path.isfile(_source):
                core.robocopy(
                    os.path.dirname(_source),
                    _destination,
                    extra_args=[os.path.basename(_source)],
                )
            # Copy directory
            else:
                if included_extensions is None:
                    extra_args = [
                        "*.pdb",
                        "*.exe",
                        "*.dll",
                        "*.elf",
                        "*.dbg",
                        "*source-info.*",
                        "*.*_Linux64_*",
                        "*.so",
                        "/S",
                    ]
                else:
                    extra_args = included_extensions
                    extra_args.append("/S")

                symbol_files = self.find_symbol_files(_source, ["*.pdb", "*.elf", "*.dbg"])
                if not _check_symbol_size(symbol_files, platform, config):
                    LOGGER.error("The symbol size seems to be incorrect, less than 100Mb")
                    LOGGER.error("Please rebuild from clean to remove stale symbols!! ")
                    raise Exception("Broken symbol")
                LOGGER.info("Copying symbols from {0} to {1}".format(_source, _destination))
                core.robocopy(_source, _destination, extra_args=extra_args)

            if core.use_bilbo():
                # bilbopy-ify the symbols
                self.bilbo.register_symbols(_destination, branch=branch, changelist=changelist)

    @collect_metrics()
    def source_index(
        self,
        platform,
        config=None,
        path=None,
        p4_port=None,
        p4_client=None,
        p4_user=None,
        use_fbenv_copy=None,
        strip_symbols=True,
        custom_p4_cmd=False,
    ):
        """
        Source indexing
        """
        LOGGER.info("Running source indexing")

        if path is None:
            path = local_paths.get_local_build_path(platform, config)
            if platform.lower() == "tool":
                path = os.path.dirname(path)

        if platform.lower() in ["ps4", "ps5"]:
            self._source_index_ps4_binaries(path, platform, config, p4_port, p4_client, p4_user)
        elif platform.lower() in [
            "win64game",
            "win64trial",
            "win64server",
            "xb1",
            "xbsx",
            "tool",
        ]:
            _source_index_win64_binaries(path, p4_port, p4_client, p4_user, custom_p4_cmd)
        elif "linux" in platform.lower() and strip_symbols:
            self.strip_linux_symbols(path, config, platform, use_fbenv_copy=use_fbenv_copy)

    def _source_index_ps4_binaries(self, path, platform, config, p4_port, p4_client, p4_user):
        """
        Source index a PlayStation elf file at path.
        """
        # Is there a benefit to only source-index the main game binary and not the other binaries?
        elf_file_patterns = tuple(f"{p}_{config}.elf" for p in ["Ps4", "Ps5", "gen4b", "gen5b"])
        try:
            elf_file = next(
                os.path.join(path, f) for f in os.listdir(path) if f.endswith(elf_file_patterns)
            )
        except StopIteration:
            raise ELIPYException(
                "Unable to find {0} {1} ELF file at {2} for source indexing.".format(
                    platform, config, path
                )
            )
        p4.P4Utils(port=p4_port, client=p4_client, user=p4_user).set_environment()

        with tempfile.TemporaryDirectory() as sym_temp_dir:
            _run_playstation_symupload(
                platform,
                [
                    "index",
                    "/f",
                    elf_file,
                    "/s",
                    sym_temp_dir,
                    "/x",
                    "P4",
                    "/i",
                    os.path.join(frostbite_core.get_tnt_root(), "Code"),
                    "/p",
                    "/l",
                    "/o",
                    "/S",
                    p4_port,
                    "/w",
                    p4_client,
                ],
            )
            source_info_xml = None
            for root, _, files in os.walk(sym_temp_dir):
                if self.PLAYSTATION_SOURCE_INDEXING_FILE in files:
                    source_info_xml = os.path.join(root, self.PLAYSTATION_SOURCE_INDEXING_FILE)
                    LOGGER.info("Found source-info file at {0}".format(source_info_xml))
                    break

            if source_info_xml:
                source_info_dest = os.path.join(path, self.PLAYSTATION_SOURCE_INDEXING_FILE)
                LOGGER.info(
                    "Copying {0} source index file {1} to {2}".format(
                        platform, source_info_xml, source_info_dest
                    )
                )
                shutil.copy2(source_info_xml, source_info_dest)
                CodeUtils.add_files_to_pushbuild(
                    platform, config, [self.PLAYSTATION_SOURCE_INDEXING_FILE]
                )
            else:
                LOGGER.error("Unable to find source index file for {0}.".format(platform))
                raise ELIPYException("Source indexing for {0} failed".format(platform))

    @retry.retry((CoreException, BrokenSymbol), tries=3, delay=6, backoff=2, logger=LOGGER)
    @collect_metrics()
    def upload_symbols_to_sym_store(
        self,
        platform,
        path=None,
        sym_store=None,
        changelist="",
        product_name="",
        log_file=False,
        symbol_files=[],
        config="",
        compress=True,
        require_source_indexing=True,
    ):
        """
        Symbol store upload. Support for two distinct stores: Microsoft and Playstation
        """
        platform = platform.lower()

        if "linux" in platform:
            LOGGER.warning("No support for a Linux symbol store")
            return

        path = path or local_paths.get_local_build_path(platform, config)
        playstation = platform in ["ps4", "ps5"]
        sym_store = sym_store or self.symbol_store_path.format("ps" if playstation else "ms")
        os.makedirs(sym_store, exist_ok=True)

        if playstation:
            self._playstation_symbol_store_upload(
                symbol_files or path,
                sym_store,
                platform,
                compress,
                log_file,
                require_source_indexing,
            )
        else:
            self.run_upload_symbols_to_sym_store(
                path=path,
                sym_store=sym_store,
                log_file=log_file,
                symbol_files=symbol_files,
                sym_store_exe=self.find_symstore_exe_file()[0],
                extra_args=["/t", product_name, "/v", changelist, "/c", path],
                exts=[".exe", "dll", "pdb", "winmd"],
                compress=compress,
            )

    @staticmethod
    def find_symstore_exe_file():
        """
        Looks like a function to get the Frostbite Package Server version of Windows SDK
        symstore.exe and symchk.exe.
        """
        package = package_utils.ensure_package_is_installed("WindowsSDK", fast_exists_check=True)
        symstore_exe = os.path.join(
            package["path"], "installed", "10", "debuggers", "x64", "symstore.exe"
        )
        symchk_exe = os.path.join(
            package["path"], "installed", "10", "debuggers", "x64", "symchk.exe"
        )
        return [symstore_exe, symchk_exe]

    @collect_metrics()
    def verify_symbol_integrity(
        self, path_to_binaries: str = None, platform: str = None, config: str = None
    ):
        """
        Run symchk.exe on supplied path or local build path.
        """
        if not path_to_binaries and not (platform and config):
            raise ELIPYException("path_to_binaries or platform and config must be supplied")

        path_to_binaries = path_to_binaries or local_paths.get_local_build_path(platform, config)
        size_limit = 2

        def size_of(pdb):
            return os.stat(pdb).st_size / (2**30)

        try:
            too_large = next(
                pdb
                for pdb in self.find_symbol_files(path_to_binaries, "pdb")
                if size_of(pdb) > size_limit
            )
        except StopIteration:
            pass
        else:
            LOGGER.info("{} is over {} GB".format(too_large, size_limit))
            # raise BrokenSymbol(
            #    "%s file size is %.3f, which > %.3f GB."
            #    % (too_large, size_of(too_large), size_limit)
            # )

        file_patterns_to_check = _win64_symbol_pattern(path_to_binaries)

        with tempfile.TemporaryDirectory() as folder_name:
            with open(os.path.join(folder_name, "q"), mode="w+") as input_file:
                input_file.writelines(file_patterns_to_check)
            LOGGER.info("verifying symbols:\n%s", "".join(file_patterns_to_check))
            args = [
                self.find_symstore_exe_file()[1],
                "/v",
                "/os",
                "/it",
                input_file.name,
                "/pf",
                "/s",
                path_to_binaries,
            ]
            try:
                core.run(args, print_std_out=True)
            except CoreException:
                raise BrokenSymbol("SYMCHK.EXE found at least one broken symbol.")

    @collect_metrics()
    def run_upload_symbols_to_sym_store(
        self,
        path=None,
        sym_store=None,
        log_file=False,
        symbol_files=[],
        sym_store_exe=None,
        extra_args=[],
        exts=[],
        compress=True,
    ):
        """
        Upload symbols to a Microsoft symbol store
        Finds all files ending with `exts` and uploads them
        Changelist is the version number.
        If logfile is true a log will be stored in the path specified.
        """
        if not symbol_files and not (path and exts):
            raise ELIPYException(
                "List of symbol files or path with symbol file name extensions must be provided."
            )
        symbol_files = symbol_files or [
            f"{s}\n"
            for s in self.find_symbol_files(path, exts)
            if not s.endswith("WinPixEventRuntime.dll")
        ]

        # Check if we should ignore PDB errors
        ignore_pdb_errors = SETTINGS.get("ignore_pdb_errors", default="false").lower() == "true"
        if ignore_pdb_errors:
            LOGGER.info("PDB errors will be ignored during symbol store upload")

        with tempfile.TemporaryDirectory() as folder_name:
            with open(os.path.join(folder_name, "q"), mode="w+") as batch_path:
                batch_path.writelines(symbol_files)
            LOGGER.info("storing symbol:\n%s", "".join(symbol_files))
            args = [
                sym_store_exe,
                "add",
                "/s",
                sym_store,
                "/o",
                "/r",
                "/f",
                f"@{batch_path.name}",
            ] + extra_args
            args += ["/compress"] if compress else []
            args += ["/d", os.path.join(path, self.SYMBOL_STORE_LOG)] if log_file else []
            attempts = 5

            for attempt in range(attempts):
                exit_code, stdout, _ = core.run(
                    args, allow_non_zero_exit_code=True, print_std_out=True, capture_std_out=True
                )
                info_msg = "{0} exited with exit code {1}".format(args, exit_code)

                # Check for PDB errors in the output
                pdb_error = False
                if exit_code > 1:
                    for line in stdout:
                        # Check for various PDB error patterns
                        is_failed_copy_pdb = "Failed to copy" in line and ".pdb" in line
                        is_symstore_error_pdb = "SYMSTORE ERROR" in line and ".pdb" in line
                        is_symstore_error_count = (
                            "SYMSTORE: Number of errors" in line and line.strip().endswith("1")
                        )

                        if is_failed_copy_pdb or is_symstore_error_pdb or is_symstore_error_count:
                            pdb_error = True
                            LOGGER.warning("PDB error detected: %s", line)

                # If we're ignoring PDB errors and this is a PDB error, treat it as success
                if exit_code in [1, 0] or (ignore_pdb_errors and pdb_error):
                    LOGGER.info(info_msg)
                    if ignore_pdb_errors and pdb_error:
                        LOGGER.info("Ignoring PDB errors as configured")
                    return
                elif attempt < attempts - 1:
                    LOGGER.warning(info_msg)
                    wait_time = 30
                    LOGGER.warning(
                        "%s/%s: waiting for release of file for %s seconds...",
                        attempt + 1,
                        attempts - 1,
                        wait_time,
                    )
                    time.sleep(wait_time)

            # Check if the final failure was due to PDB errors
            final_pdb_error = False
            for line in stdout:
                is_failed_copy_pdb = "Failed to copy" in line and ".pdb" in line
                is_symstore_error_pdb = "SYMSTORE ERROR" in line and ".pdb" in line
                is_symstore_error_count = (
                    "SYMSTORE: Number of errors" in line and line.strip().endswith("1")
                )

                if is_failed_copy_pdb or is_symstore_error_pdb or is_symstore_error_count:
                    final_pdb_error = True
                    break

            # If we're ignoring PDB errors, don't raise an exception for PDB errors
            if ignore_pdb_errors and (pdb_error or final_pdb_error):
                LOGGER.warning("Ignoring PDB errors as configured, continuing despite errors")
                return

            raise ELIPYException(
                f"Made {attempts} attempts to run {args}, but got an exit code > 1 every time."
            )

    @collect_metrics()
    def upload_game_binary(
        self,
        platform,
        package_type,
        config,
        data_changelist,
        code_changelist,
        data_branch,
        code_branch,
    ):
        """
        Finds the renamed game binary and uploads it to symstore.
        """
        local_frosty_path = local_paths.get_local_frosty_path()
        game_binaries = self.find_symbol_files(local_frosty_path, SETTINGS.get("game_binaries"))

        if config.lower() != "retail":
            self.upload_symbols_to_sym_store(
                changelist=code_branch + code_changelist + data_branch + data_changelist,
                product_name="frosty.{}.{}.{}.{}.{}".format(
                    platform, config, package_type, code_branch, data_branch
                ),
                symbol_files=game_binaries,
                platform=platform,
                config=config,
                path=local_frosty_path,
            )

    @staticmethod
    def find_symbol_files(path: str, exts: Union[Iterable[str], str]) -> List[str]:
        """
        Help function for finding all files with symbol extension in path
        """
        if os.path.isdir(path):
            if not isinstance(exts, str):
                exts = tuple(exts)

            symbol_files = []

            for current, _, files in os.walk(path):
                symbol_files.extend(os.path.join(current, f) for f in files if f.endswith(exts))

            return symbol_files
        else:
            raise ELIPYException("Can't find symbols in a non directory path, {}".format(path))

    @collect_metrics()
    def fake_ooawrap_bin(
        self,
        binary_path=None,
        platform="win64game",
        config="retail",
        changelist="",
        branch="",
        denuvo_wrapping=True,
        use_fbenv_copy=None,
    ):
        """
        Fake ooawrapped bin to store readable symbol
        """
        LOGGER.info("Faking ooa wrapped bin to store usable symbol file")
        if binary_path is None:
            binary_path = local_paths.get_local_build_path(platform, config)

        file_suffix = (
            "prot.exe"
            if denuvo_wrapping
            else local_paths.get_executable_name_pattern(platform, config)
        )
        prot_bin = self.find_symbol_files(binary_path, [file_suffix])

        if not prot_bin:
            raise ELIPYException("Did not find binary to wrap at {}".format(binary_path))

        for bin_file in prot_bin:
            LOGGER.info("Found binary file, {}".format(bin_file))

            fake_path = os.path.join(frostbite_core.get_tnt_root(), "ooaFake")
            if os.path.isdir(fake_path):
                shutil.rmtree(fake_path, ignore_errors=True)

            core.robocopy(
                os.path.dirname(bin_file),
                fake_path,
                extra_args=[os.path.basename(bin_file), "/R:10", "/W:10", "/s"],
            )
            fake_prot_path = os.path.join(fake_path, os.path.basename(bin_file))
            game_binaries = SETTINGS.get("game_binaries")
            for binary in game_binaries:
                if "trial" in platform.lower() and "trial" in binary.lower():
                    fake_game_bin_path = os.path.join(fake_path, binary)
                    fake_file = binary + "_fake"
                    fake_game_fakebin_path = os.path.join(fake_path, fake_file)
                elif "trial" not in platform.lower() and "trial" not in binary.lower():
                    fake_game_bin_path = os.path.join(fake_path, binary)
                    fake_file = binary + "_fake"
                    fake_game_fakebin_path = os.path.join(fake_path, fake_file)

            LOGGER.info("Renaming {} to {}".format(fake_prot_path, fake_game_bin_path))
            os.rename(fake_prot_path, fake_game_bin_path)

            core.run(
                [
                    os.path.join(
                        frostbite_core.get_tnt_root(),
                        "Build",
                        "Bin",
                        "DenuvoPEPatcher",
                        "DenuvoPEPatcher.exe",
                    ),
                    fake_game_bin_path,
                ],
                print_std_out=True,
            )

            LOGGER.info("Uploading fake ooa wrapped bin to ms symstore")
            self.upload_symbols_to_sym_store(
                path=fake_path,
                changelist=changelist,
                product_name="{}.{}".format(branch, platform),
                platform=platform,
                config=config,
            )

            LOGGER.info("Renaming {} to {}".format(fake_game_bin_path, fake_game_fakebin_path))
            os.rename(fake_game_bin_path, fake_game_fakebin_path)
            # Copy fake binary to same dir as local game binaries
            local_build_path = local_paths.get_local_build_path(platform, config)

            LOGGER.info("Storing binary {} in {}".format(fake_game_fakebin_path, local_build_path))
            core.robocopy(fake_path, local_build_path, extra_args=[fake_file])

            if use_fbenv_copy is None:
                use_fbenv_copy = frostbite_core.minimum_fb_version(year=2019, version_nr=1)

            if use_fbenv_copy:
                CodeUtils.add_files_to_pushbuild(platform, config, file_list=[fake_file])

    @staticmethod
    @collect_metrics()
    def strip_linux_symbols(build_path, config, platform="linux64server", use_fbenv_copy=None):
        """
        Strips linux symbols
        """
        if not os.path.exists(build_path):
            LOGGER.error("Build path {0} does not exits".format(build_path), exc_info=True)
            raise ELIPYException("Invalid build path {0} when striping symbols".format(build_path))

        package = package_utils.ensure_package_is_installed(
            "UnixCrossTools", fast_exists_check=True
        )
        root_path = os.path.join(package["path"], "installed", "Cygwin64")
        gnu_tools_location = os.path.join(root_path, "usr", "x86_64-pc-linux-gnu", "bin")

        if not os.path.exists(root_path):
            possible_root_paths = [
                os.path.join(package["path"], "installed", "crosstool"),
                os.path.join(package["path"], "installed", "crosstool_x64"),
            ]

            for path in possible_root_paths:
                if os.path.exists(path):
                    root_path = path
                    break
            else:
                raise ELIPYException(
                    "Cannot find UnixCrossTools installation at locations: {0} or {1}".format(
                        *possible_root_paths
                    )
                )

            gnu_tools_location = _find_tools_location(root_path)
            if gnu_tools_location is None:
                raise ELIPYException(
                    "Cannot find objcopy.exe and strip.exe in any subdirectory "
                    "of the installed directory: {0}".format(root_path)
                )

        # Location of some DLLs that Cygwin depends on,
        # we need to run in this directory so the GNU tools can find it.
        cygwin_dll_location = os.path.join(root_path, "bin")

        mainfile = None
        if platform.lower() in ["linux", "linux64"]:
            mainfile = "{0}.Main_{1}_{2}".format(fbenv_layer.get_exe_name(), "Linux64", config)
        else:
            mainfile = "{0}.Main_{1}_{2}_Server".format(
                fbenv_layer.get_exe_name(), "Linux64", config
            )
        files = [mainfile]

        if not os.path.exists(cygwin_dll_location):
            LOGGER.error("{0} does not exits".format(cygwin_dll_location), exc_info=True)
            raise ELIPYException("Missing {0} when striping symbols".format(cygwin_dll_location))

        files_with_path = _get_on_disk_filenames_to_strip(files, build_path)

        LOGGER.debug("Symbol Stripping started from {0}".format(files))
        _extract_strip_link(files_with_path, gnu_tools_location, cygwin_dll_location)
        LOGGER.debug("Symbol Stripping complete from {0}".format(files))

        if use_fbenv_copy is None:
            use_fbenv_copy = frostbite_core.minimum_fb_version(year=2019, version_nr=1)

        if use_fbenv_copy:
            for file_name in files:
                CodeUtils.add_files_to_pushbuild(platform, config, file_list=[file_name + ".dbg"])

    def copy_symbols_to_kobold_inbox(self, branch, changelist, platform, config):
        """
        This method is used to copy the symbols to the Kobold inbox.
        The method then calls the backup_symbols method to copy the symbols to the Kobold inbox.
        The backup_symbols method is responsible for copying the symbol files from the source to
        the destination. The source is determined based on the platform and config parameters.
        """
        target_path = "{}-{}-{}-{}".format(changelist, platform, config, branch)
        kobold_settings = SETTINGS.get("kobold", default={})
        if not kobold_settings:
            LOGGER.warning("Kobold settings are not set")
            return

        kobold_inbox_paths = kobold_settings.get("inbox_paths")[0]
        branch_inbox_path = kobold_inbox_paths.get(branch, kobold_inbox_paths.get("default"))
        if branch_inbox_path is None:
            raise ValueError(f"No kobold inbox found for branch {branch}")
        kobold_inbox_path = os.path.join(branch_inbox_path, target_path)

        included_extensions = kobold_settings.get("included_file_extensions", None)
        LOGGER.info(
            "Copying symbols ,%s, to Kobold inbox: %s", str(included_extensions), kobold_inbox_path
        )
        self.backup_symbols(
            branch=branch,
            changelist=changelist,
            platform=platform,
            config=config,
            destination=kobold_inbox_path,
            included_extensions=included_extensions,
        )


def _find_tools_location(directory):
    for root, dirs, files in os.walk(directory):
        if "objcopy.exe" in files and "strip.exe" in files:
            return root

    return None


def _run_playstation_symupload(platform: str, arguments: List[str]):
    def check_env_path(filename):
        path_env = os.environ.get("PATH")

        # Split the path environment into individual paths
        paths = path_env.split(";")

        # Search for the PS5 SDK path
        ps5_sdk_path = None
        found_path, found_file = False, False
        for path in paths:
            if "ps5sdk" in path.lower():
                ps5_sdk_path = path
                found_path = True
                break
        if ps5_sdk_path:
            for root, dirs, files in os.walk(ps5_sdk_path):
                for file in files:
                    if file == filename:
                        found_file = True
                        break
        return [found_path, found_file]

    exe_name = f"{'orbis' if platform == 'ps4' else 'prospero'}-symupload.exe"
    should_retry = True
    retry_count = 0
    retry_attempt = 2
    while should_retry:
        try:
            result = core.run([exe_name] + arguments, print_std_out=True, capture_std_out=True)
        except FileNotFoundError as error:
            LOGGER.info(os.environ.get("PATH"))
            [found_path, found_file] = check_env_path(exe_name)
            if found_path and not found_file:
                error_message = f"{exe_name} is not in the PATH."
            else:
                error_message = "SDK path is missing from PATH."
            if retry_count < retry_attempt:
                LOGGER.info(error_message)
                LOGGER.info("Reinstalling PS5 SDK.")
                sdk_utils.install_sdks_elipy(platform="ps5")
                retry_count += 1
                continue
            raise ELIPYException(error_message) from error
        except CoreException as error:
            LOGGER.warning("Retry for Exception: {}".format(error.args))
            # Adding retry for intermittent issue
            if "Could not create a symbol server at" in error.args:
                if retry_count < retry_attempt:
                    LOGGER.info("Retrying for error: {}".format(error.args))
                    retry_count += 1
                    continue
            raise error
        should_retry = False

    return result


def _source_index_win64_binaries(
    path, p4_port=None, p4_client=None, p4_user=None, custom_p4_cmd=False
):
    """
    Source indexing win64 binaries
    Implementation from batch script, UploadSymbols.bat:
    """

    def generate_srcsrvini(srcsrvini, p4_port):
        server = p4_port.split(".", maxsplit=1)[0] + ":" + p4_port.split(":", maxsplit=1)[1]
        parser = configparser.SafeConfigParser(allow_no_value=True)
        with open(srcsrvini, "w") as configfile:
            configfile.write("[variables]\n")
        parser.read(srcsrvini)
        parser.set("variables", "ELIPYSERVER", p4_port)
        parser.set("variables", "STOCKHOLMSERVER", server)
        parser.set("variables", "STOCKHOLMSERVER_FQDN", p4_port)
        with open(srcsrvini, "w") as configfile:
            parser.write(configfile)
        with open(srcsrvini, "r") as config_file:
            print(config_file.read())

    source_code_path = os.path.join(frostbite_core.get_tnt_root(), "Code")

    srcsrvini = os.path.join(tempfile.gettempdir(), "srcsrvini")
    try:
        p4_port = p4_port.replace(
            "dicela-p4edge-fb.la.ad.ea.com:2001", "dicela-p4edge-fb.ad.ea.com:2001"
        )
        generate_srcsrvini(srcsrvini, p4_port)
        if not custom_p4_cmd:
            # Use p4_ssindex.ps1 script to correctly handle P4 server address discrepancies
            # between what's configured and what's returned by p4 info
            p4_ssindex_script = os.path.join(
                os.path.dirname(os.path.dirname(__file__)), "scripts", "p4_ssindex.ps1"
            )
            if not os.path.exists(p4_ssindex_script):
                # Fall back to the regular command if script not found
                LOGGER.info("p4_ssindex.ps1 script not found, using standard p4 command")
                os.environ["P4_CMD"] = "p4.exe -p {} -c {} -u {}".format(
                    p4_port, p4_client, p4_user
                )
            else:
                LOGGER.info("Using p4_ssindex.ps1 script to properly handle P4 server address")
                os.environ["P4_CMD"] = "powershell.exe -nologo -file {} info".format(
                    p4_ssindex_script
                )
        LOGGER.info("P4_CMD set to: {}".format(os.environ["P4_CMD"]))
        package = package_utils.ensure_package_is_installed("WindowsSDK", fast_exists_check=True)
        ssindex = os.path.join(
            package["path"],
            "installed",
            "10",
            "debuggers",
            "x64",
            "srcsrv",
            "ssindex.cmd",
        )
        cmd = [
            ssindex,
            "-ini={0}".format(srcsrvini),
            "-source={0}".format(source_code_path),
            "-symbols={0}".format(path),
            "-system=p4",
            "/debug",
        ]
        core.run(cmd, print_std_out=True)
        os.remove(srcsrvini)
    except Exception as exc:
        LOGGER.info("Failed to set server in srcsrv ini, continuing. Cause: {}".format(exc))


def _get_on_disk_filenames_to_strip(files, build_path):
    matched_files_on_disk = []
    files_lower = [f.lower() for f in files]
    dir_content = os.listdir(build_path)

    # for every file on the hard drive, in it's native case
    for _file in dir_content:
        # if it matches with the two files we're looking for in lowercase
        if _file.lower() in files_lower:
            # add it to the list of files we're looking to strip
            matched_files_on_disk.append(_file)

    matched_files_on_disk_lower = [f.lower() for f in matched_files_on_disk]
    for _expected_file in files_lower:
        if _expected_file not in matched_files_on_disk_lower:
            raise ELIPYException(
                "{0} does not exist in {1}, unable to strip linux symbols. (found {2})".format(
                    _expected_file, build_path, dir_content
                )
            )

    return [os.path.join(build_path, f) for f in matched_files_on_disk]


def _extract_strip_link(files, gnu_tools_location, cygwin_dll_location):
    objcopy = os.path.join(gnu_tools_location, "objcopy.exe")
    strip = os.path.join(gnu_tools_location, "strip.exe")

    for file_to_strip in files:
        file_to_strip_dbg = file_to_strip + ".dbg"
        commands = {
            "Extracting debug symbols from": [
                objcopy,
                "--only-keep-debug",
                file_to_strip,
                file_to_strip_dbg,
            ],
            "Stripping": [strip, "-s", file_to_strip],
            "Adding debug link to": [
                objcopy,
                "--add-gnu-debuglink={0}".format(file_to_strip_dbg),
                file_to_strip,
            ],
        }

        for message, command in commands.items():
            LOGGER.info("{} {}".format(message, file_to_strip))
            core.run(
                command,
                working_dir=cygwin_dll_location,
                allow_non_zero_exit_code=True,
                print_std_out=True,
            )


def _check_symbol_size(symbol_files, platform, config):
    ret = True
    correct_size = 100 * 1024 * 1024
    regex = "{0}_{1}".format(platform, config)
    biggest_symbol_files = [files for files in symbol_files if regex in files]
    for symbol in biggest_symbol_files:
        if os.path.getsize(symbol) <= correct_size:
            ret = False
            break
    return ret


def _win64_symbol_pattern(path_to_binaries):
    return [
        f"{path_to_binaries}\\{file_pattern}\n"
        for file_pattern in [
            f"{os.environ.get('EXE_MAIN_NAME')}*.exe",
            "Engine*.dll",
            "Extension*.dll",
        ]
    ]


def add_server_to_srcsrvini(perforce: p4.P4Utils, srcsrvini):
    """
    Try adding server to srcsrc.ini in a non failing way
    File is not checked in only edited
    """
    try:
        perforce.edit(srcsrvini)
        parser = configparser.SafeConfigParser(allow_no_value=True)
        parser.read(srcsrvini)
        parser.set("variables", "ELIPYSERVER", perforce.port)
        with open(srcsrvini, "w") as configfile:
            parser.write(configfile)
    except Exception as exc:
        LOGGER.info("Failed to set server in srcsrv ini, continuing. Cause: {}".format(exc))
        perforce.revert()
