"""
test_vault.py
"""

import pytest
from mock import patch, MagicMock
import elipy2.vault
import elipy2.exceptions


class TestVault(object):
    def setup_method(self):
        self.patcher_symbols_upload_ms = patch(
            "elipy2.symbols.SymbolsUtils.upload_symbols_to_sym_store"
        )
        self.mock_symbols_upload_ms = self.patcher_symbols_upload_ms.start()
        # mock the os.mkdirs step
        self.patcher_makedirs = patch("os.makedirs")
        self.mock_makedirs = self.patcher_makedirs.start()
        self.patcher_mkdir = patch("os.mkdir")
        self.mock_mkdir = self.patcher_mkdir.start()

        self.patcher_upload_metrics = patch("elipy2.telemetry.upload_metrics")
        self.mock_upload_metrics = self.patcher_upload_metrics.start()

        # mock core.robocopy
        self.patcher_robocopy = patch("elipy2.vault.core.robocopy")
        self.mock_robocopy = self.patcher_robocopy.start()

        # mock core._run and core.run
        self.patcher_run = patch("elipy2.vault.core.run")
        self.mock_run = self.patcher_run.start()
        self.mock_run.return_value = (0, [], [])

        # mock os.path.exists to prevent real file system access
        self.patcher_exists = patch("elipy2.core.os.path.exists")
        self.mock_exists = self.patcher_exists.start()
        self.mock_exists.return_value = True

        # mock os.path.isdir
        self.patcher_isdir = patch("elipy2.core.os.path.isdir")
        self.mock_isdir = self.patcher_isdir.start()
        self.mock_isdir.return_value = False

    def teardown_method(self):
        self.patcher_robocopy.stop()
        self.patcher_run.stop()
        self.patcher_symbols_upload_ms.stop()
        self.patcher_makedirs.stop()
        self.patcher_mkdir.stop()
        self.patcher_upload_metrics.stop()
        self.patcher_exists.stop()
        self.patcher_isdir.stop()

    @patch("elipy2.vault.core.delete_folder")
    def test_save_symbols_locally_clean(self, mock_clean):
        # Ensure robocopy mock is working
        self.mock_robocopy.return_value = None
        elipy2.vault.save_symbols_locally("from/here", "to/here")
        self.mock_robocopy.assert_called_once_with("from/here", "to/here")
        mock_clean.assert_called_once_with("to/here")

    @patch("elipy2.vault.core.md5_check")
    def test_vault_anything_with_checksum(self, mock_md5):
        elipy2.vault._vault_anything_with_checksum("from/here", "to/here", md5_validation=True)
        self.mock_robocopy.assert_called_once()
        mock_md5.assert_called_once()

    @patch("elipy2.vault.core.md5_check")
    def test_vault_anything_with_checksum_no_dest(self, mock_md5):
        # The os.path.exists is already mocked in setup
        elipy2.vault._vault_anything_with_checksum("from/here", "to/here", md5_validation=False)
        self.mock_robocopy.assert_called_once()
        assert mock_md5.call_count == 0

    def test_save_vaulting_logs(self):
        elipy2.vault._save_vaulting_logs("not/a/url", "myfile.txt", "location")
        self.mock_run.assert_called_once()

    def test_save_symbols_locally(self):
        elipy2.vault.save_symbols_locally("from/here", "to/here", clean=False)
        self.mock_robocopy.assert_called_once_with("from/here", "to/here")

    @patch("elipy2.frostbite.package_utils.ensure_package_is_installed")
    @patch("elipy2.filer_paths.get_code_build_platform_path")
    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.vault._vault_anything_with_checksum")
    @patch("elipy2.vault._save_vaulting_logs")
    @patch("os.path.join")
    def test_vault_symbols_nolog(
        self,
        mock_join,
        mock_logs,
        m_checksum,
        mock_code_path,
        mock_code_plt_path,
        mock_package_installed,
    ):
        # Mock the path join to return a path that exists (mocked in setup)
        mock_join.return_value = "/fake/path/source-info.xml"
        mock_code_path.return_value = "/fake/path"
        mock_code_plt_path.return_value = "/fake/platform/path"

        elipy2.vault.vault_symbols(
            source="from/here",
            vault_destination="to/here",
            code_branch="branch",
            change_list="12345",
            product_name="fake product",
            build_url=None,
            build_id="a build id",
            platform="ps4",
            symstore_vault_destination="to/here",
            md5_validation=True,
        )
        assert mock_logs.call_count == 0

    @patch("elipy2.vault.validate_build_files")
    @patch("elipy2.vault._vault_anything_with_checksum")
    @patch("elipy2.vault._save_vaulting_logs")
    def test_vault_build(self, mock_logs, mock_checksum, mock_validate):
        elipy2.vault.vault_build(
            source="from/here",
            destination="to/here",
            platform="fake platform",
            build_url="not/a/url",
            build_id="a build id",
        )
        mock_checksum.assert_called_once_with("from/here", "to/here", md5_validation=True)
        filename = "vault-build-" + "a build id" + ".log"
        mock_logs.assert_called_once_with(
            build_url="not/a/url", filename=filename, destination="to/here"
        )

    @patch("elipy2.vault.validate_build_files")
    @patch("elipy2.vault._vault_anything_with_checksum")
    @patch("elipy2.vault._save_vaulting_logs")
    def test_vault_build_no_log(self, mock_logs, mock_checksum, mock_validate):
        elipy2.vault.vault_build(
            source="from/here",
            destination="to/here",
            platform="fake platform",
            build_id="a build id",
            build_url=None,
        )
        mock_checksum.assert_called_once_with("from/here", "to/here", md5_validation=True)
        filename = "vault-build-" + "a build id" + ".log"
        assert mock_logs.call_count == 0

    @patch("elipy2.vault.os.walk")
    def test_validate_build_files(self, mock_walk):
        mock_walk.return_value = [
            ("/foo", ("bar",), ("baz",)),
            ("/foo/bar", (), ("spam", "fil1", "fil2")),
        ]

        elipy2.vault.validate_build_files(source="test\\\\source", platform="win64")
        mock_walk.assert_called_once_with("test\\\\source")

    @patch("elipy2.vault.os.walk")
    def test_validate_build_files_notset(self, mock_walk):
        mock_walk.return_value = [
            ("/foo", ("bar",), ("baz",)),
            ("/foo/bar", (), ("spam", "fil1", "fil2")),
        ]
        elipy2.vault.validate_build_files(source="test\\\\source", platform="xb1")

    @patch("elipy2.vault.os.walk")
    def test_validate_build_files_no_files(self, mock_walk):
        mock_walk.return_value = [
            ("/foo", ("bar",), ("baz",)),
            ("/foo/bar", (), ("spam", "fil1", "fil2")),
        ]

        with pytest.raises(elipy2.exceptions.ELIPYException):
            elipy2.vault.validate_build_files(source="test\\\\source", platform="ps4")
            mock_walk.assert_called_once_with("test\\\\source")

    @patch("elipy2.vault.core.md5_check")
    @patch("elipy2.vault.symbols.SymbolsUtils")
    @patch("elipy2.vault.filer_paths.get_code_build_path")
    @patch("elipy2.vault.filer_paths.get_code_build_platform_path")
    def test_vault_symbols_with_symstore_error_ignored(
        self, mock_platform_path, mock_code_path, mock_symbols_utils, mock_md5
    ):
        """Test that SYMSTORE PDB errors are ignored when ignore_pdb_errors is True"""
        # Mock the symbols upload to raise an exception with SYMSTORE error
        mock_symbols_instance = mock_symbols_utils.return_value
        mock_symbols_instance.upload_symbols_to_sym_store.side_effect = Exception(
            "SYMSTORE ERROR: Class: Runtime. Desc: Failed to copy file.pdb to server Error 0:"
        )

        # Mock path functions
        mock_code_path.return_value = "/fake/code/path"
        mock_platform_path.return_value = "/fake/platform/path"

        # Mock settings to ignore PDB errors
        with patch("elipy2.vault.SETTINGS") as mock_settings:
            mock_settings.get.return_value = "true"

            # This should not raise an exception
            elipy2.vault.vault_symbols(
                source="from/here",
                vault_destination="to/here",
                code_branch="branch",
                change_list="12345",
                product_name="test product",
                build_url=None,
                build_id="test_build",
                platform="win64",
                symstore_vault_destination="symstore/here",
                md5_validation=True,
            )

    @patch("elipy2.vault.vault_build")
    @patch("elipy2.vault.vault_symbols")
    def test_vault(self, mock_sym, mock_build):
        elipy2.vault.vault(
            source="from/here",
            destination="to/here",
            platform="fake platform",
            destination_path="to/here",
            change_list="999999",
            product_name="ps4",
            code_branch="branch",
            build_url="not/a/url",
            build_id="a build id",
            md5_validation=True,
            dry_run=False,
        )
        mock_sym.assert_called_once_with(
            build_id="a build id",
            build_url="not/a/url",
            vault_destination="to/here",
            platform="fake platform",
            symstore_vault_destination="to/here",
            change_list="999999",
            code_branch="branch",
            product_name="ps4",
            source="from/here",
            md5_validation=True,
            dry_run=False,
        )

        mock_build.assert_called_once_with(
            source="from/here",
            destination="to/here",
            platform="fake platform",
            build_url="not/a/url",
            build_id="a build id",
            md5_validation=True,
            dry_run=False,
        )

    @patch("elipy2.vault.validate_build_files", MagicMock())
    @patch("elipy2.vault._vault_anything_with_checksum")
    @patch("elipy2.vault._save_vaulting_logs")
    def test_vault_build_dry_run(self, mock_logs, mock_checksum):
        elipy2.vault.vault_build(
            source="from/here",
            destination="to/here",
            platform="fake platform",
            build_id="a build id",
            build_url="https://not/a/url",
            dry_run=True,
        )
        assert mock_checksum.call_count == 0
        assert mock_logs.call_count == 0

    @patch("elipy2.frostbite.package_utils.ensure_package_is_installed", MagicMock())
    @patch("elipy2.filer_paths.get_code_build_platform_path")
    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.vault._vault_anything_with_checksum")
    @patch("elipy2.vault._save_vaulting_logs")
    def test_vault_symbols_dry_run(self, mock_logs, mock_checksum, mock_path, mock_platform_path):
        elipy2.vault.vault_symbols(
            source="from/here",
            vault_destination="to/here",
            code_branch="branch",
            change_list="12345",
            product_name="fake product",
            build_url="https://not/a/url",
            build_id="a build id",
            platform="ps4",
            symstore_vault_destination="to/here",
            md5_validation=True,
            dry_run=True,
        )
        mock_path.return_value = "path"
        mock_platform_path.return_value = "some/other/path"
        assert mock_checksum.call_count == 0
        assert mock_logs.call_count == 0
