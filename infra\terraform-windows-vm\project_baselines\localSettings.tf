#*************************************************************
#  Sets up the initial needs to provide a vspehere instance
#*************************************************************
# Point to our provider
# https://www.terraform.io/docs/providers/vsphere/index.html
# *************************************************************
terraform {
  required_version = ">= 1.0.0"
  required_providers {
    vsphere = {
      version = "= 2.12.0" #locking Version to 2.12.0 to avoid introducing breaks
    }
  }
}
provider "vsphere" {
  alias          = "dice"
  user           = var.vsphere_user
  password       = var.vsphere_password
  vsphere_server = "vc.dice.ad.ea.com"
  # if you have a self-signed cert or not
  allow_unverified_ssl = true
  persist_session      = true
}
provider "vsphere" {
  alias          = "criterion"
  user           = var.vsphere_user
  password       = var.vsphere_password
  vsphere_server = "oh-vcenter1.ad.ea.com"
  # if you have a self-signed cert or not
  allow_unverified_ssl = true
  persist_session      = true
}
provider "vsphere" {
  alias                = "eala"
  user                 = var.vsphere_user
  password             = var.vsphere_password
  vsphere_server       = "eala-vcenter.la.ad.ea.com"
  allow_unverified_ssl = true
  persist_session      = true
}
# *************************************************************
# Terraform state artifactory backup.
# https://www.terraform.io/docs/backends/types/artifactory.html
# *************************************************************
terraform {
  backend "http" {
  }
}
# *************************************************************
# Environmental Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "vsphere_user" {}
variable "vsphere_password" {}
