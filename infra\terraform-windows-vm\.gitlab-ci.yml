stages:
  - pre-validate
  - load-project-pipeline
  - trigger-shared-pipeline

include:
  - local: /yml/terraform.yaml
  - local: /yml/python.yaml
  - local: /yml/style.yml
  - local: /yml/terraform_operation.yml

variables:
  ANSIBLE_BRANCH:
    value: master
    description: Ansible project branch name to enable execution of playbooks from another branch

  ANSIBLE_EXTRA_ARGS:
    value: ""
    description: You can add extra arguments to the Ansible run here to help (e.g. debugging add -vvv)

  TARGET_PROJECT:
    value: ""
    description: >
      Specify a single project to execute all steps for.
      This value should match the `PROJECT_NAME` in each projects .gitlab-ci.yml (e.g. kingston_rel, bct_ps, ********************)

  FORCE_RUN_ANSIBLE_ALL:
    value: "no"
    description: >
      Put yes to force ansible step to run on ALL VMs in the project and not just the newly created ones.

  FORCE_RUN_ANSIBLE_VMs:
    value: ""
    description: >
      Input a string of comma separated VM ids to force Ansible to run on them. VM(s) should exist in the project.
      E.g. xy2-abcde,xy2-bcdef, xy2-edcab.

generate-project-pipelines:
  stage: load-project-pipeline
  id_tokens:
    VAULT_JWT_TOKEN:
      aud: https://ess.ea.com
  image:
    name: mcr.artifacts.ea.com/powershell:lts-7.4-alpine-3.17
  variables:
    VAULT_SERVER_URL: https://ess.ea.com
    VAULT_NAMESPACE: cds-dre-prod
    VAULT_AUTH_ROLE: "gl-dre-cobra-silverback"
    VAULT_AUTH_PATH: "jwt/gitlab"
    ARTIFACTORY_URL: https://artifacts.ea.com/artifactory/api/pypi/index-pypi-remote/simple
  secrets:
    SILVERBACK_CONFIG_ACCESS_TOKEN:
      vault: cobra/automation/gitlab/silverback-configs/read_only/TOKEN_VALUE@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    DOCKER_AUTH_CONFIG:
      vault: artifacts/automation/dre-docker-federated/ro/DOCKER_AUTH_CONFIG@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
  script:
    - chmod +x scripts/lib/generate-child-pipelines-with-secrets.sh
    - scripts/lib/generate-child-pipelines-with-secrets.sh
  artifacts:
    paths:
      - dynamic-pipelines/secrets.yml

trigger-shared-pipeline:
  stage: trigger-shared-pipeline
  variables:
    PARENT_PIPELINE_SOURCE: $CI_PIPELINE_SOURCE
  trigger:
    include:
      - artifact: dynamic-pipelines/secrets.yml
        job: generate-project-pipelines
    forward:
      pipeline_variables: true
    strategy: depend
