@echo off

::<PERSON>ript for installing and setting up the environment needed to run elipy.
::Dependent on running in an fb environment
::It will install virtualenv in current python path, create an virtual environment, activate it,
::set the yml path if needed and install elipy and studio script packages from pypi

::Can be run by the following command
::TnT\Bin\fbcli\cli.bat win64 && path\to\this\script\setup.bat
::Path to scrip can either be absolute or relative to tnt root

::Variables that needs updating are the yaml file name, yml_file_name and the studio script package name, studio_script_pkg
::If studio scripts are not installed as a package elipy config path needs replacing and the studio script pip comand removed
echo [INFO] [%date%, %time%] Validating input...
echo.
if "%1"=="" (
    echo "Needs yaml file name as parameter"
    exit /b 0
)

echo [INFO] [%date%, %time%] Setting up environment variables...
echo.
set studio_script_pkg=dice_elipy_scripts
set yml_file_name=%1
set p4_py_dir=%GAME_ROOT%\tnt\bin\python
set virtual_dir=%GAME_ROOT%\Python\virtual
set elipy_py_scripts=%virtual_dir%\Scripts
if "%AF2_USER%"=="" goto use_artifactory1
if "%AF2_TOKEN%"=="" goto use_artifactory1

echo [INFO] [%date%, %time%] AF2_USER and AF2_TOKEN set, using authenticated artifactory2 repository
set trusted_host=artifacts.ea.com

:: url encode the @ char in AF2_USER
for /f "usebackq delims=" %%A in (`python -c "import sys;print(sys.argv[1].replace('@','%%40'))" "%AF2_USER%"`) do set "AF2_USER=%%A"

set PIP_INDEX_URL=https://%AF2_USER%:%AF2_TOKEN%@%trusted_host%/artifactory/api/pypi/dre-pypi-federated/simple
set EXTRA_INDEX_URL=https://%AF2_USER%:%AF2_TOKEN%@%trusted_host%/artifactory/api/pypi/index-pypi-remote/simple
goto end

:use_artifactory1
echo [INFO] [%date%, %time%] AF2_USER or AF2_TOKEN not set, attempting using artifactory1 repository
set trusted_host=artifacts.at.ea.com
set PIP_INDEX_URL=https://%trusted_host%/artifactory/api/pypi/dreeu-pypi-virtual/simple

:end
echo [INFO] [%date%, %time%] Environment variables set...

:: Pip settings
set PIP_LOG=%workspace_root%\dev\logs\%~n0-verbose.log

::Delete empty folder in python dir, since those messes with pip. (Folders get left there empty by switching streams)
echo [INFO] [%date%, %time%] Deleting empty folders in Python dir...
echo.
for /f "delims=" %%d in ('dir %p4_py_dir% /s /b /ad ^| sort /r') do rd "%%d" 2> NUL

if "%ELIPY_CONFIG%"=="" (
    set ELIPY_CONFIG=python\virtual\Lib\site-packages\%studio_script_pkg%\yml\%yml_file_name%
)

echo [INFO] [%date%, %time%] Calling `%elipy_py_scripts%\activate`...
echo.
set path=%path%;%elipy_py_scripts%
call %elipy_py_scripts%\activate

echo [INFO] [%date%, %time%] Done!
