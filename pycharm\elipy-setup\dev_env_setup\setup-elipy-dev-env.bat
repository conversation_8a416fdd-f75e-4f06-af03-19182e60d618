@echo off

::<PERSON><PERSON><PERSON> for installing and setting up the environment needed to develop elipy
::Call this script from a running fbcli environment (found at TnT\Bin\fbcli\cli.bat)

::The environment variables are set using the dev_env_setup/dev_environment_config
::file however a customised filepath can be passed as argument.
if NOT "%1"=="" (
    set conf_file===%1
) else (
    set conf_file== %~dp0dev_environment_config
)

:: pip settings
:: set PIP_LOG=%workspace_root%\dev\logs\%~n0-verbose.log

echo [INFO] [%date%, %time%] Setting up environment variables...
::Prompt for config values defined in conf_file
for /f "delims== tokens=1,2" %%G in (%conf_file%) do call set /p "%%G=Enter %%G (default -> %%H):" || call set "%%G=%%H"
:: Output set values for user clarity
echo "ELIPY_CONFIG=%ELIPY_CONFIG%"
echo "elipy_scripts_pkg=%elipy_scripts_pkg%"
echo "elipy_core_pkg=%elipy_core_pkg%"
echo "target_venv_dir=%target_venv_dir%"
echo "p4_python_source_dir=%p4_python_source_dir%"
echo "target_pip_version=%target_pip_version%"

:: Check for authentication variables
echo [INFO] [%date%, %time%] Testing AF2_USER and AF2_TOKEN set...

if "%AF2_USER%"=="" goto use_artifactory1
if "%AF2_TOKEN%"=="" goto use_artifactory1

echo [INFO] [%date%, %time%] AF2_USER and AF2_TOKEN set, using authenticated artifactory2 repository
set trusted_host=artifacts.ea.com

:: url encode the @ char in AF2_USER
for /f "usebackq delims=" %%A in (`python -c "import sys;print(sys.argv[1].replace('@','%%40'))" "%AF2_USER%"`) do set "AF2_USER=%%A"

set PIP_INDEX_URL=https://%AF2_USER%:%AF2_TOKEN%@%trusted_host%/artifactory/api/pypi/dre-pypi-federated/simple
set PIP_EXTRA_INDEX_URL=https://%AF2_USER%:%AF2_TOKEN%@%trusted_host%/artifactory/api/pypi/index-pypi-remote/simple
goto end

:use_artifactory1
echo [INFO] [%date%, %time%] AF2_USER or AF2_TOKEN not set, attempting using artifactory1 repository
set trusted_host=artifacts.at.ea.com
set PIP_INDEX_URL=https://%trusted_host%/artifactory/api/pypi/dreeu-pypi-virtual/simple

:end
echo [INFO] [%date%, %time%] Environment variables set...
echo.

python -m pip install --user pip --upgrade pip==%target_pip_version% --trusted-host %trusted_host% virtualenv<=20.23.0 setuptools>=60.5.0
python -m virtualenv --verbose --system-site-packages %target_venv_dir%

::Delete empty folders in perforce python dir, since those mess with pip. (Folders get left there empty by switching streams)
echo [INFO] [%date%, %time%] Deleting empty folders in Python dir...
echo.
for /f "delims=" %%d in ('dir %p4_python_source_dir% /s /b /ad ^| sort /r') do rd "%%d" 2> NUL

echo [INFO] [%date%, %time%] Calling `%target_venv_dir%\Scripts\activate`...
echo.
set path=%path%;%target_venv_dir%\Scripts
call %target_venv_dir%\Scripts\activate

echo [INFO] [%date%, %time%] Installing PIP...
python -m pip --timeout 60 install --upgrade pip==%target_pip_version% --disable-pip-version-check --cache-dir %target_venv_dir%\.pip-cache

echo [INFO] [%date%, %time%] Installing elipy-scripts package...
python -m pip install --user pip -r %elipy_scripts_pkg%\requirements.txt
python -m pip install --user pip -e %elipy_scripts_pkg%

echo [INFO] [%date%, %time%] Installing elipy core package...
python -m pip install --user pip -r %elipy_core_pkg%\requirements.txt
python -m pip install --user pip -e %elipy_core_pkg%

::Avoid one pytest error by installing the sphinx dependency outside of requirements.txt
python -m pip install --user pip sphinx_rtd_theme

echo [INFO] [%date%, %time%] Done!]
