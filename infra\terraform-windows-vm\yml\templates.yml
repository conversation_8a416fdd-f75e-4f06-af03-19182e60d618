#****************************************
#           TEMPLTATE PIPES
#****************************************
.get_id_token:
  id_tokens:
    VAULT_JWT_TOKEN:
      aud: https://ess.ea.com
    VAULT_ID_TOKEN:
      aud: https://ess.ea.com
  secrets:
    # Artifactory
    ARTIFACTORY_USERNAME:
      vault: artifacts/automation/dre-generic-federated/ro/username@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    ARTIFACTORY_PASSWORD:
      vault: artifacts/automation/dre-generic-federated/ro/reference_token@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_VAR_ARTIFACTORY_USER:
      vault: artifacts/automation/dre-generic-federated/ro/username@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_VAR_ARTIFACTORY_PASSWORD:
      vault: artifacts/automation/dre-generic-federated/ro/reference_token@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false

    # Python
    PYTHON_USER:
      vault: artifacts/automation/dre-pypi-federated/ro/username@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    PYTHON_PASSWORD:
      vault: artifacts/automation/dre-pypi-federated/ro/reference_token@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false

    # AWS
    AWS_ACCESS_KEY_ID:
      vault: cobra/automation/accounts/drecobra_aws_svc/ACCESS_KEY_ID@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    AWS_SECRET_ACCESS_KEY:
      vault: cobra/automation/accounts/drecobra_aws_svc/SECRET_ACCESS_KEY@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    AWS_DEFAULT_REGION:
      vault: cobra/automation/accounts/drecobra_aws_svc/AWS_DEFAULT_REGION@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false

    # GitLab
    SILVERBACK_CONFIG_ACCESS_TOKEN:
      vault: cobra/automation/gitlab/silverback-configs/read_only/TOKEN_VALUE@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false

    PAT_TOKEN_DRE_EA:
      vault: cobra/automation/azure/dre-battlefield-azure/ado-pat/ado_pat@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    PAT_TOKEN_EA_BATTLEFIELD:
      vault: cobra/automation/azure/dre-battlefield-azure/ea-battlefield/ado_pat@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false

    SENTRY_DSN:
      vault: cobra/automation/services/terraform-windows-vm/SENTRY_DSN@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_VAR_domain_gnt_admin:
      vault: cobra/automation/services/terraform-windows-vm/TF_VAR_domain_gnt_admin@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_VAR_domain_gnt_password:
      vault: cobra/automation/services/terraform-windows-vm/TF_VAR_domain_gnt_password@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_VAR_vsphere_server:
      vault: cobra/automation/services/terraform-windows-vm/TF_VAR_vsphere_server@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_HTTP_USERNAME:
      vault: cobra/automation/services/terraform-windows-vm/TF_HTTP_USERNAME@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_HTTP_PASSWORD:
      vault: cobra/automation/services/terraform-windows-vm/TF_HTTP_PASSWORD@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_VAR_winrm_ssl_cert:
      vault: cobra/automation/services/terraform-windows-vm/cert@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_VAR_winrm_ssl_chain:
      vault: cobra/automation/services/terraform-windows-vm/chain@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_VAR_winrm_ssl_key:
      vault: cobra/automation/services/terraform-windows-vm/key@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_VAR_non_domain_local:
      vault: cobra/automation/services/terraform-windows-vm/non_domain_local@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false

    TF_VAR_vsphere_password:
      vault: cobra/automation/vsphere/global/PASSWORD@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    TF_VAR_vsphere_user:
      vault: cobra/automation/vsphere/global/EMAIL@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false

    VC_PASS:
      vault: cobra/automation/vsphere/global/PASSWORD@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    VC_USER:
      vault: cobra/automation/vsphere/global/EMAIL@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false

  variables:
    TF_VAR_commit_sha: $CI_COMMIT_SHA
    TF_VAR_commit_url: $CI_JOB_URL
    TF_VAR_commit_user: $GITLAB_USER_LOGIN
    TF_VAR_project_dir: $CI_PROJECT_DIR
    TF_VAR_domain_admin: "$AD_JOINER_DOMAIN\\$AD_JOINER_USERNAME"
    TF_VAR_domain_password: "$AD_JOINER_PASSWORD"
    TF_VAR_local_username: "$MAIN_ACCOUNT_DOMAIN\\$MAIN_ACCOUNT_USERNAME"
    TF_VAR_local_password: "$MAIN_ACCOUNT_PASSWORD"

    TF_VAR_dlln_admin_username: "$AD_JOINER_USERNAME"
    TF_VAR_dlln_local_username: "$MAIN_ACCOUNT_USERNAME"

    # Misc
    VM_DOMAIN: "$AD_JOINER_DOMAIN_TARGET"
    kinit_user: "${MAIN_ACCOUNT_USERNAME}@${MAIN_ACCOUNT_KERBEROS_REALM}"

    VAULT_SERVER_URL: https://ess.ea.com
    VAULT_ADDR: https://ess.ea.com
    VAULT_NAMESPACE: cds-dre-prod
    VAULT_AUTH_ROLE: "gl-dre-cobra-silverback"
    VAULT_AUTH_PATH: "jwt/gitlab"


.common_setups_terraform:
  image:
    name: $dockerterraform
  extends: ['.get_id_token']
  before_script:
    - cd $CI_PROJECT_DIR/$WORKING_DIR
    - rm -rf .terraform
    - terraform --version
    - BACKEND_PATH="https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/terraform/state/${PROJECT_NAME}"
    - terraform init -backend=true -get=true -input=false -backend-config="address=${BACKEND_PATH}" -backend-config="lock_address=${BACKEND_PATH}/lock" -backend-config="unlock_address=${BACKEND_PATH}/lock" -backend-config="username=${TF_HTTP_USERNAME}" -backend-config="password=${TF_HTTP_PASSWORD}" -backend-config="lock_method=POST" -backend-config="unlock_method=DELETE" -backend-config="retry_wait_min=5"
    - shopt -s expand_aliases
    - alias convert_report="jq -r '([.resource_changes[]?.change.actions?]|flatten)|{\"create\":(map(select(.==\"create\"))|length),\"update\":(map(select(.==\"update\"))|length),\"delete\":(map(select(.==\"delete\"))|length)}'"

.script_run_ansible:
  extends: ['.get_id_token']
  before_script:
    - |
      echo -e "machine artifacts.ea.com\nlogin ${PYTHON_USER}\npassword ${PYTHON_PASSWORD}" > $HOME/.netrc
      pip3 install -i https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated/simple dre-vsphere-tools==1.0.178
      git clone -b $ANSIBLE_BRANCH --single-branch https://gitlab-ci-token:$<EMAIL>/dre-cobra/ansible/windows.git/
      export ANSIBLE_COMMIT_HASH=$(git -C ./windows rev-parse HEAD)

      set +e
      python ansible/write_ansible_files.py --project $PROJECT_NAME --vc_pass $VC_PASS --vc_user $VC_USER --vc_host $VC_HOST --node_info_file $NODE_INFO_FILE
      EXIT_CODE=$?

      set -e # We allow the error 557799 to pass through
      if [ $EXIT_CODE -ne 0 ] && [ $EXIT_CODE -ne 557799 ]; then
        exit $EXIT_CODE
      fi

      cd windows
  script:
    - VAULT_AUTH_PATH="auth/jwt/gitlab/login"
    - sh ansible.sh $ansible_main_module $ANSIBLE_EXTRA_ARGS
  allow_failure:
    exit_codes: [557799]

# for step not require terraform (i.e only on master branch)
.common_setups_condition:
  rules:
    - if: $CI_COMMIT_BRANCH == 'master' && $TARGET_PROJECT == $PROJECT_NAME && ( $PARENT_PIPELINE_SOURCE == "schedule" || $PARENT_PIPELINE_SOURCE == "web" )
    - if: $CI_COMMIT_BRANCH == 'master' && ($TARGET_PROJECT == null || $TARGET_PROJECT == "") && $PARENT_PIPELINE_SOURCE == "push"
      changes:
        paths:
          - $WORKING_DIR/**/* # a directory and all its subdirectories (mainly for runners folder)
          - scripts/*
          - yml/*
          - modules/**/*
          - attache/*
    - if: $CI_COMMIT_BRANCH != 'master' && $TARGET_PROJECT == $PROJECT_NAME && ( $PARENT_PIPELINE_SOURCE == "schedule" || $PARENT_PIPELINE_SOURCE == "web" )

.file_change_rules:
  rules:
    - if: $CI_COMMIT_BRANCH == 'master' && ($TARGET_PROJECT == null || $TARGET_PROJECT == "") && $PARENT_PIPELINE_SOURCE == "push"
      changes:
        paths:
          - $WORKING_DIR/**/* # a directory and all its subdirectories (mainly for runners folder)
          - scripts/*
          - yml/*
          - modules/**/*
          - attache/*
    - if: $CI_COMMIT_BRANCH != 'master' && ($TARGET_PROJECT == null || $TARGET_PROJECT == "") && $PARENT_PIPELINE_SOURCE == "push"
      changes:
        compare_to: "refs/heads/master"
        paths:
          - $WORKING_DIR/**/* # a directory and all its subdirectories (mainly for runners folder)
          - scripts/*
          - yml/*
          - modules/**/*
          - attache/*
    # for scheduled pipeline, it always run
    - if: $TARGET_PROJECT == $PROJECT_NAME && $PARENT_PIPELINE_SOURCE == "schedule"
    # for manually click "Run Pipeline", it always run
    - if: $TARGET_PROJECT == $PROJECT_NAME && $PARENT_PIPELINE_SOURCE == "web"

.validation_steps:
  stage: validate
  extends: ['.common_setups_terraform', '.file_change_rules']
  script:
    - terraform validate

.plan_steps_common:
  stage: plan
  extends: ['.common_setups_terraform', '.file_change_rules']
  resource_group: "${WORKING_DIR}_terraform"
  artifacts:
    when: always
    paths:
      - planfile-$PROJECT_NAME
      - $CI_PROJECT_DIR/output.log
      - $CI_PROJECT_DIR/planfile-$PROJECT_NAME.json
      - $CI_PROJECT_DIR/tf_debug.log
    reports:
      terraform: $CI_PROJECT_DIR/planfile-$PROJECT_NAME.json

.plan_steps:
  stage: plan
  extends: ['.common_setups_terraform', '.plan_steps_common']
  script:
    # we need manual mask passwordd, tf sensitive protection only works for pure variable not cover this if set as internal variable in resource
    - terraform plan -lock-timeout=5m -parallelism="$PLAN_PARALLELISM" -out "$CI_PROJECT_DIR/planfile-$PROJECT_NAME" | sed 's/passwordd:[^ ]*/passwordd:<sensitive>/' | tee $CI_PROJECT_DIR/output.log
    - terraform show --json "$CI_PROJECT_DIR/planfile-$PROJECT_NAME" | convert_report > "$CI_PROJECT_DIR/planfile-$PROJECT_NAME.json"
  tags:
    - cobra-runner

.plan_steps_runner:
  stage: plan
  extends: ['.common_setups_terraform', '.plan_steps_common']
  script:
    - apk add --no-cache vault libcap jq
    - setcap cap_ipc_lock= /usr/sbin/vault
    - export VAULT_TOKEN="$(vault write -field=token ${VAULT_AUTH_LOGIN_PATH} role=${VAULT_AUTH_ROLE} jwt=${VAULT_JWT_TOKEN})"
    - export TF_VAR_losangeles_gitlab_runner_token=$(vault kv get --format=json ${SECRET_TOP}/${SECRET_NAME_LA} | jq -r '.data.data.token')
    - export TF_VAR_criterion_gitlab_runner_token=$(vault kv get --format=json ${SECRET_TOP}/${SECRET_NAME_CRIT} | jq -r '.data.data.token')
    - export TF_VAR_dice_gitlab_runner_token=$(vault kv get --format=json ${SECRET_TOP}/${SECRET_NAME_DICE} | jq -r '.data.data.token')
    # we need manual mask passwordd, tf sensitive protection only works for pure variable not cover this if set as internal variable in resource
    - terraform plan -lock-timeout=5m -parallelism="$PLAN_PARALLELISM" -out "$CI_PROJECT_DIR/planfile-$PROJECT_NAME" | sed 's/passwordd:[^ ]*/passwordd:<sensitive>/' | tee $CI_PROJECT_DIR/output.log
    - terraform show --json "$CI_PROJECT_DIR/planfile-$PROJECT_NAME" | convert_report > "$CI_PROJECT_DIR/planfile-$PROJECT_NAME.json"

.prepare_config:
  stage: fetch-configs
  extends: ['.get_id_token', '.file_change_rules']
  image:
    name: $dockerterraform
  script:
    - 'echo https://gitlab.ea.com/api/v4/projects/38098/repository/files/${SILVERBACK_CONFIG_JSON_FILE}/raw?ref=main'
    - 'curl --location --header "PRIVATE-TOKEN: $SILVERBACK_CONFIG_ACCESS_TOKEN" "https://gitlab.ea.com/api/v4/projects/38098/repository/files/${SILVERBACK_CONFIG_JSON_FILE}/raw?ref=main" --output $CI_PROJECT_DIR/${WORKING_DIR}/${PROJECT_NAME}.json'
    - if grep -q '{"message":"404' $CI_PROJECT_DIR/${WORKING_DIR}/${PROJECT_NAME}.json; then echo "ERROR! Failed to fetch valid config file! Inspect config file URL!"; exit 1; fi
  artifacts:
    paths:
      - $CI_PROJECT_DIR/${WORKING_DIR}/${PROJECT_NAME}.json

.apply_steps:
  stage: apply
  extends: ['.common_setups_terraform', '.common_setups_condition']
  resource_group: "${WORKING_DIR}_terraform"
  script:
    # we need manual mask passwordd, tf sensitive protection only works for pure variable not cover this if set as internal variable in resource
    - terraform apply -lock-timeout=5m -parallelism="$APPLY_PARALLELISM" -input=false "$CI_PROJECT_DIR/planfile-$PROJECT_NAME" | sed 's/passwordd:[^ ]*/passwordd:<sensitive>/'
    - terraform output -json nodes > $CI_PROJECT_DIR/$NODE_INFO_FILE
  artifacts:
    when: always
    paths:
      - $CI_PROJECT_DIR/$NODE_INFO_FILE
      - $CI_PROJECT_DIR/tf_debug.log
  tags:
    - cobra-runner

.nodeless_apply_steps:
  stage: apply
  extends: ['.common_setups_terraform', '.common_setups_condition']
  resource_group: "${WORKING_DIR}_terraform"
  script:
    # we need manual mask passwordd, tf sensitive protection only works for pure variable not cover this if set as internal variable in resource
    - terraform apply -lock-timeout=5m -parallelism="$APPLY_PARALLELISM" -input=false "$CI_PROJECT_DIR/planfile-$PROJECT_NAME" | sed 's/passwordd:[^ ]*/passwordd:<sensitive>/'
  artifacts:
    when: always
    paths:
      - $CI_PROJECT_DIR/tf_debug.log

.attache_vmdk_step:
  stage: attache
  extends: ['.get_id_token', '.common_setups_condition']
  resource_group: "${WORKING_DIR}_attache"
  image:
    name: $dockercontroller
  script:
    - python attache/attache_vmdk.py --project $PROJECT_NAME --vc_host $VC_HOST --vc_pass $VC_PASS --vc_user $VC_USER --node_info_file $NODE_INFO_FILE
  artifacts:
    when: always
    paths:
      - $CI_PROJECT_DIR/attache_vms.txt
      - $CI_PROJECT_DIR/$NODE_INFO_FILE
  tags:
    - cobra-runner

# sync runs on a windows private runner "TERRA_RUNNER" tagged machines
.sync_vmdk_step:
  stage: sync
  extends: ['.get_id_token', '.common_setups_condition']
  resource_group: "${WORKING_DIR}_sync"
  image:
    name: $dockerpowershell
  secrets:
    DOCKER_AUTH_CONFIG:
      vault: artifacts/automation/dre-docker-federated/ro/DOCKER_AUTH_CONFIG@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
  script:
    - Install-PackageProvider -Name NuGet -MinimumVersion ********* -Force
    - C:\Windows\system32\WindowsPowerShell\v1.0\powershell.exe -NoProfile -NonInteractive -File ./attache/run_sync.ps1 -project $PROJECT_NAME -password "$env:TF_VAR_local_password" -username $env:TF_VAR_local_username -vm_file $CI_PROJECT_DIR/$NODE_INFO_FILE -project_dir $CI_PROJECT_DIR -vc_host $VC_HOST
  tags:
    - TERRA_RUNNER
  artifacts:
    when: always
    paths:
      - $CI_PROJECT_DIR/$NODE_INFO_FILE

# Main ansible step to run ansible playbooks for GlaaS Runners
.run_ansible_step:
  stage: ansible
  extends: ['.script_run_ansible', '.common_setups_condition']
  resource_group: "${WORKING_DIR}_ansible"
  image:
    name: $dockercontroller
  artifacts:
    when: always
    paths:
      - $CI_PROJECT_DIR/$NODE_INFO_FILE
  tags:
    - cobra-runner
