# elipy-setup
## install-elipy.bat

### Syntax
```bash
install-elipy.bat <config.yml> ["<elipy-script version>"] ["<elipy2 version>"]
```

- **config.yml**
    - The locations of your elipy config yml file.
    - Examples: `cobra.yml`
- **elipy-script version**
    - What version of the elipy-scripts package do you want to install.
    - Using `""` will pull latest
    - Examples: `1.0.0`, `4.0.7433`, `4.0a1.dev7432`

- **elipy2 version**
    - What version of elipy2 package do you want to install.
    - Using `""` will pull latest
    - Examples: `1.0.0`, `13.0.5419`, `12.2a1.dev5418`



### Info
These 2 scripts are used to download python packages from different artifactory repository

In Jenkins, production job normally call
``` >D:\dev\ci\install-elipy.bat elipy_$project.yml ```
which invoke production release for elipy-script and elipy2-core built from master branches

For test purpose, we can use elipy-script and elipy2-core from non-master built versions
``` >D:\dev\ci\install-elipytest.bat elipy_$project.yaml "version1" "$version2" ```
Above example uses variable version1 for elipy-script and version2 for elipy2-core to setup elipy env.
By using "" (empty string) as first argument, this keeps using production verison elipy-script
By using production version elipy2, the 2nd argument can be skipped


To get correct value for version1 and version2, you need to go to gitalb-ci pipeline with correct SHA1 and search for version number submitted to artifactory.
Usually, it is in semantic version.

## setup-elipy-env.bat
```bash
setup-elipy-env.bat <config.yml>
```
Batch file arguments are positional

- **config.yml**
    - The locations of your elipy config yml file.
    - Examples: `cobra.yml`

### info
This script sets up python enviroment by using elipy packages and project yaml file
``` >D:\dev\ci\setup-elipy-env.bat elipy_$project.yml ```

After running previous two steps, you should be able to call elipy functions
```>elipy --location [dice|criterion|test|irt] $elipy_function [parameters...]```
Wisely choose location value, if not sure, check elipy_$project.yml

## Setting up a development environment for elipy-scripts and elipy2

The `dev_env_setup\setup-elipy-dev-env.bat` script will create a virtual environment and install in it everything you need to develop `elipy-scripts` and `elipy2`.

It uses the `elipy-setup\dev_env_setup\dev_environment_config` to prompt the user details on their development environment (while providing reasonable default values). A custom `dev_environment_config` filepath can be passed as an argument when calling the `setup-elipy-dev-env.bat` if necessary.

### Quick dev start:

- Open a frostbite cli instance (found at `TnT\Bin\fbcli\cli.bat` in a local P4 workspace)
- Ensure you have necessary minimum local files synced (see [here](https://docs.google.com/document/d/1nergYj3s8ydIeazLuJpUlAH4xI0ZTm2_MV2pMOl8jqY/edit?tab=t.0#heading=h.cmu0ara50ij3) for the specific files needed in the workspace for the cli.bat to boot correctly)
- Execute the `elipy-setup\dev_env_setup\setup-elipy-dev-env.bat` file. The user will be prompted to enter values related to their development setup.
- Once the `setup-elipy-dev-env.bat` has been executed, the virtual env will have been created at the elipy_py_scripts path. It will also be activated in the current frostbite cli instance. *This virtual env can also be activated within or used by an IDE, allowing unit testing and IDE assisted development. Don't forget this step!*
- Update the script path in the ELIPY_CONFIG file to point to your dev repository of `elipy-scripts`.

Now you can add new modules, run unit tests and develop.

> Note: Calling elipy directly e.g. `elipy info` will only work from the frostbite cli instance, as it depends on `fbenv` and the frostbite environment.

## Artifactory authentication
The various batch scripts in this repository check for the existence of AF2_USER and AF2_TOKEN env variables.
If they are absent, the artifactory 1 upstream which requires no authentication will be used.
If they are set, the artifactory 2 upstreams which requires authentication will be used.
