#!/usr/bin/env python3
"""
Test script to verify the deleter comprehensive cleanup fix.
This script tests that the backward compatibility options work correctly.
"""

import sys
import subprocess
import os
from pathlib import Path

def run_command(cmd, capture_output=True):
    """Run a command and return the result"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=capture_output, 
            text=True, 
            cwd=r"d:\vscode\pycharm\elipy-scripts"
        )
        return result
    except Exception as e:
        print(f"Error running command '{cmd}': {e}")
        return None

def test_help_output():
    """Test that help output shows comprehensive-cleanup but not the deprecated options"""
    print("Testing help output...")
    
    try:
        # Import the deleter module to test help
        sys.path.insert(0, r"d:\vscode\pycharm\elipy-scripts")
        from click.testing import C<PERSON><PERSON>unner
        from dice_elipy_scripts.deleter import cli
        
        runner = CliRunner()
        result = runner.invoke(cli, ["--help"])
        
        help_text = result.output
        
        # Check that comprehensive-cleanup is shown
        if "--comprehensive-cleanup" in help_text:
            print("✅ --comprehensive-cleanup option is visible in help")
        else:
            print("❌ --comprehensive-cleanup option is NOT visible in help")
            return False
            
        # Check that deprecated options are hidden
        if "--comprehensive-category" not in help_text:
            print("✅ --comprehensive-category option is hidden from help")
        else:
            print("❌ --comprehensive-category option is visible in help (should be hidden)")
            return False
            
        if "--comprehensive-branch" not in help_text:
            print("✅ --comprehensive-branch option is hidden from help")
        else:
            print("❌ --comprehensive-branch option is visible in help (should be hidden)")
            return False
            
        # Check help text mentions processing all categories and branches
        if "all configured categories and branches" in help_text:
            print("✅ Help text mentions processing all categories and branches")
        else:
            print("❌ Help text does not mention processing all categories and branches")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing help output: {e}")
        return False

def test_command_validation():
    """Test that the command accepts the problematic combination that was failing"""
    print("\nTesting command validation...")
    
    try:
        sys.path.insert(0, r"d:\vscode\pycharm\elipy-scripts")
        from click.testing import CliRunner
        from dice_elipy_scripts.deleter import cli
        
        runner = CliRunner()
        
        # Test the exact command that was failing in Jenkins
        # Note: Using --dry-run to avoid actually doing any cleanup
        test_commands = [
            # Original failing command (simulated)
            ["--no-shift-delete", "--include-path-retention", "--comprehensive-cleanup", "--dry-run"],
            
            # Test with deprecated parameters (should work but show warning)
            ["--comprehensive-cleanup", "--comprehensive-category", "code", "--comprehensive-branch", "main", "--dry-run"],
            
            # Test comprehensive cleanup alone (should work)
            ["--comprehensive-cleanup", "--dry-run"],
        ]
        
        for i, cmd_args in enumerate(test_commands, 1):
            print(f"\nTest {i}: {' '.join(cmd_args)}")
            
            # Mock the settings to avoid dependency issues
            try:
                result = runner.invoke(cli, cmd_args, catch_exceptions=False)
                
                if result.exit_code == 0:
                    print(f"✅ Command {i} executed successfully")
                    
                    # Check for deprecation warning in test 2
                    if i == 2 and "DEPRECATED" in result.output:
                        print("✅ Deprecation warning shown for deprecated parameters")
                    elif i == 2:
                        print("⚠️  Deprecation warning not shown (this might be okay depending on logging level)")
                        
                else:
                    print(f"❌ Command {i} failed with exit code {result.exit_code}")
                    print(f"Output: {result.output}")
                    if result.exception:
                        print(f"Exception: {result.exception}")
                    return False
                    
            except Exception as e:
                # We expect some exceptions due to missing dependencies
                # The important thing is that we don't get validation errors about missing parameters
                error_msg = str(e)
                if "comprehensive-category" in error_msg and "comprehensive-branch" in error_msg and "required" in error_msg:
                    print(f"❌ Command {i} failed with the original validation error: {error_msg}")
                    return False
                else:
                    print(f"✅ Command {i} failed with expected dependency error (not validation error): {error_msg}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing command validation: {e}")
        return False

def test_syntax():
    """Test that the Python file compiles correctly"""
    print("\nTesting syntax...")
    
    result = run_command("python -m py_compile dice_elipy_scripts\\deleter.py")
    
    if result and result.returncode == 0:
        print("✅ Python file compiles correctly")
        return True
    else:
        print("❌ Python file has syntax errors")
        if result:
            print(f"Error: {result.stderr}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Deleter Comprehensive Cleanup Fix")
    print("=" * 50)
    
    os.chdir(r"d:\vscode\pycharm\elipy-scripts")
    
    tests = [
        ("Syntax Check", test_syntax),
        ("Help Output", test_help_output),
        ("Command Validation", test_command_validation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fix should resolve the Jenkins issue.")
        return 0
    else:
        print("💥 Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
