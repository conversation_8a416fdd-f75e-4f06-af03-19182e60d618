"""
datapreflight.py

DOCUMENTATION:
 - https://docs.google.com/document/d/1BJDoqDZG4VNCyvEUwaH5pnXMCoPTWZr-62CXaHRY7G0/edit?usp=sharing
"""
import click
import copy
import os
import re
from typing import Dict, List
from dice_elipy_scripts.utils.datapreflight_utils import (
    get_dbx_assets_to_cook,
    extract_layers_from_file_list,
)
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.preflight_utils import (
    raise_if_cl_not_exists,
    raise_if_wrong_stream,
    truncate_log_list,
)
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.state_utils import import_avalanche_data_state
from elipy2 import avalanche, data, filer, LOGGER, p4, running_processes
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


# pylint: disable=line-too-long
@click.command(
    "datapreflight",
    short_help="Unshelves and builds a given changelist for validation.",
)
@click.argument("p4_port")
@click.argument("p4_client")
@click.argument("platform")
@click.argument("pending_changelist")
@click.option("--user", default=None, help="Perforce user name.")
@click.option("--assets", default=None, help="Which data assets to build.", multiple=True)
@click.option(
    "--asset",
    default=["preflightlevels"],
    help="Deprecated parameter. Use --assets instead.",
    multiple=True,
)
@click.option(
    "--pipeline-branch",
    default=None,
    help="Branch to fetch pipeline from.",
    required=True,
)
@click.option(
    "--pipeline-changelist",
    default=None,
    help="Which pipeline CL to use.",
    required=True,
)
@click.option("--data-branch", default="data", help="Branch to fetch Avalanche state from.")
@click.option(
    "--datadir",
    default="data",
    help="Specify which data directory to use (relative to GAME_ROOT).",
)
@click.option("--import-avalanche-state", is_flag=True, help="Imports Avalanche state from filer.")
@click.option("--data-changelist", default=123, help="Data cl to run preflight on.")
@click.option(
    "--cook-dbx-assets",
    is_flag=True,
    help="Cook extra assets based on unshelved dbx files",
)
@click.option(
    "--fail-on-dbx-cook",
    is_flag=True,
    help="Fail if needed when cooking individual dbx assets",
)
@click.option(
    "--clean-master-version-check",
    is_flag=True,
    help="Run clean on master version update.",
)
@click.option("--pipeline-args", multiple=True, help="Pipeline arguments for data build.")
@click.option("--indexing-args", multiple=True, help="Pipeline arguments for data indexing cook.")
@click.option(
    "--do-warmup/--not-warmup",
    default=False,
    help="--do-warmup to warm up AWS agent; --not-warmup/not set, normal preflight.",
)
@click.option(
    "--validate-direct-references",
    default=False,
    help="Validate dbx files that are not referenced in the main preflight asset.",
)
@click.option(
    "--clean-index",
    default=False,
    help="Run the indexing step with the -clean flag.",
)
@click.option(
    "--codebuild-fileshare",
    help="Elipy config key to find buildshare in alternate_build_shares.",
    default=None,
)
@click.option(
    "--use-local-codebuilds",
    default=False,
    help="Use builds that are already in the machine or fetch them from the filer",
)
@click.option(
    "--skip-unshelve",
    default=False,
    help="Skip any p4 unshelve in case it was executed already",
)
@click.option(
    "--skip-revert",
    default=False,
    help="Skip any p4 revert in case those are not necessary for the build",
)
@click.option(
    "--use-response-file",
    default=False,
    help="Cook using a response file instead of in chunks of assets",
)
@click.option(
    "--content-layer",
    default="",
    help="Specific content layer to run an extra cook for during datapreflight in addition to source layer. Should be DEPREDECATED.",
)
@click.option(
    "--content-layers",
    multiple=True,
    default=None,
    help="Content layers to cook during datapreflight. Overrides --content-layer option.",
)
@click.option(
    "--auto-content-layers",
    default=False,
    help="Automatically detect content layers in the unshelved files",
)
@click.option(
    "--p4-unshelve-file",
    default=None,
    help="File containing text output of a p4 unshelve command.",
)
@click.option(
    "--collect-mdmps",
    default=True,
    help="Should mdmps be collected when cook fails",
)
# pylint: enable=line-too-long
# pylint: disable=too-many-locals
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    p4_port,
    p4_client,
    platform,
    pending_changelist,
    user,
    assets,
    asset,
    pipeline_branch,
    pipeline_changelist,
    data_branch,
    datadir,
    import_avalanche_state,
    data_changelist,
    cook_dbx_assets,
    fail_on_dbx_cook,
    clean_master_version_check,
    pipeline_args,
    indexing_args,
    do_warmup,
    validate_direct_references,
    clean_index,
    codebuild_fileshare,
    use_local_codebuilds,
    skip_unshelve,
    skip_revert,
    use_response_file,
    content_layer,
    content_layers,
    auto_content_layers,
    p4_unshelve_file,
    collect_mdmps,
):
    """
    Preflight the data build
    """
    LOGGER.start_group("Setup build environment")

    # Adding sentry tags
    add_sentry_tags(__file__, "preflight")

    # Kill any unexpected running processes
    running_processes.kill()

    # Use deprecated param if necessary
    if not assets:
        assets = asset

    # Unshelve pending changelist
    perforce = p4.P4Utils(port=p4_port, user=user, client=p4_client)
    if not skip_revert:
        perforce.revert()

    LOGGER.end_group()

    if not do_warmup:
        raise_if_cl_not_exists(perforce, pending_changelist)
        raise_if_wrong_stream(perforce, pending_changelist)

    try:
        file_list = []
        if p4_unshelve_file:
            file_list = get_p4_file_data(p4_unshelve_file)
        elif not do_warmup and not skip_unshelve:
            file_list = perforce.unshelve(pending_changelist)

        # Fetch pipeline build from filer, skip using bilbo
        _filer = filer.FilerUtils()
        if not use_local_codebuilds:
            LOGGER.info("Fetching pipeline build from filer")
            LOGGER.info(codebuild_fileshare)
            _filer.fetch_code(
                pipeline_branch,
                pipeline_changelist,
                "pipeline",
                "release",
                use_bilbo=False,
                target_build_share=codebuild_fileshare,
            )
        else:
            LOGGER.info("Using local builds")

        LOGGER.start_group("Setup data environment")
        # Build data
        builder = data.DataUtils(platform, list(assets))
        # Set data dir
        builder.set_datadir(datadir)

        extra_args = []

        # Import previous Avalanche state
        if import_avalanche_state:
            extra_args = import_avalanche_data_state(
                data_branch, pipeline_branch, platform, _filer, data_changelist
            )
        LOGGER.end_group()

        extra_args += list(pipeline_args)

        cook_args = {
            "pipeline_args": extra_args,
            "indexing_args": list(indexing_args),
            "collect_mdmps": collect_mdmps,
            "clean_index": clean_index,
            "clean_master_version_check": clean_master_version_check,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }

        # Unnecessary to cook individual dbxs on platforms other than win64
        if platform != "win64" or not file_list:
            cook_dbx_assets = False
            validate_direct_references = False

        if auto_content_layers:
            content_layers = extract_layers_from_file_list(
                p4_unshelved_files=file_list, datadir=datadir
            )

        if content_layer and not content_layers and not content_layer.lower() == "source":
            content_layers = [content_layer, "source"]

        LOGGER.info("Updating index")
        indexing_args = copy.deepcopy(cook_args)
        indexing_args["only_indexing"] = True
        indexing_args["skip_indexing"] = False
        builder.cook(**indexing_args)
        if "source" not in (x.lower() for x in content_layers):
            content_layers = (*content_layers, "source")

        for layer in content_layers:
            if cook_dbx_assets:
                individual_asset_cooks(
                    file_list,
                    datadir,
                    layer,
                    platform,
                    cook_args,
                    fail_on_dbx_cook,
                    use_response_file,
                )
            if validate_direct_references:
                individual_asset_validate_direct_refs(
                    file_list, datadir, layer, platform, cook_args, use_response_file
                )
            if layer.lower() == "source":
                LOGGER.info("Cooking source layer")
                builder.cook(**cook_args)
            else:
                LOGGER.info("Cooking content layer '%s'", layer)
                layer_args = copy.deepcopy(cook_args)
                layer_args["pipeline_args"] += ["-activeContentLayer", layer]
                builder.cook(**layer_args)

        avalanche.set_avalanche_build_status(
            code_changelist=pipeline_changelist,
            data_changelist=data_changelist,
            data_branch=data_branch,
            platform=platform,
        )
    finally:
        if not do_warmup and not skip_revert:
            perforce.revert()


def individual_asset_cooks(
    file_list: List[str],
    datadir: str,
    content_layer: str,
    platform: str,
    cook_args: Dict,
    fail_on_dbx_cook: bool,
    use_response_file: bool,
) -> None:
    """
    Run a cook on the individual assets if they are are found in the pending changelist
    """
    individual_dbx_assets_to_cook = get_dbx_assets_to_cook(file_list, datadir, content_layer)
    if individual_dbx_assets_to_cook:
        layer_args = copy.deepcopy(cook_args)
        if content_layer != "" and content_layer.lower() != "source":
            layer_args["pipeline_args"] += ["-activeContentLayer", content_layer]
        cook_submitted_dbx_assets(
            individual_dbx_assets_to_cook,
            platform,
            layer_args,
            fail_on_dbx_cook,
            use_response_file,
        )


def individual_asset_validate_direct_refs(
    file_list: List[str],
    datadir: str,
    content_layer: str,
    platform: str,
    cook_args: Dict,
    use_response_file: bool,
) -> None:
    """
    Run validateDirectReferences RPC on the individual assets
    if they are are found in the pending changelist
    """
    individual_dbx_assets_to_validate = get_dbx_assets_to_cook(
        file_list, datadir, content_layer, exclude_files=False
    )
    if individual_dbx_assets_to_validate:
        layer_args = copy.deepcopy(cook_args)
        if content_layer != "" and content_layer.lower() != "source":
            layer_args["pipeline_args"] += ["-activeContentLayer", content_layer]
        validate_direct_dbx_references(
            individual_dbx_assets_to_validate,
            platform,
            layer_args,
            use_response_file,
        )


def get_p4_file_data(p4_output_file: str) -> list:
    """
    Get a list of file data from the output of a p4 command
    """
    p4_files = []
    if not os.path.exists(p4_output_file):
        LOGGER.info("File {} does not exist.".format(p4_output_file))
        return p4_files

    with open(p4_output_file) as file:
        for line in file.readlines():
            matches = re.findall("^(//.*)#.* opened for (\\w+)$", line)
            if len(matches) == 1 and len(matches[0]) == 2:
                file_dict = {
                    b"depotFile": str.encode(matches[0][0]),
                    b"action": str.encode(matches[0][1]),
                }
                p4_files.append(file_dict)
    truncate_log_list(p4_files, "Parsed the following files out of '{}'".format(p4_output_file))
    return p4_files


def cook_submitted_dbx_assets(
    individual_dbx_assets: List[str],
    platform: str,
    cook_args: Dict,
    fail_on_dbx_cook: bool,
    use_response_file: bool,
) -> None:
    """
    Select the best way to cook the individual dbx assets.

    :param individual_dbx_assets: A list of asset names to build.
    :param platform: What platform are we building for.
    :param cook_args: Arguments to pass to the cook command.
    :param fail_on_dbx_cook: Set if a failure here will fail the overall job.
    :param use_response_file: Use a response file to cook the assets
    :return: None (raises an exception if failing).
    """
    LOGGER.info("Building individual dbx assets...")
    if len(individual_dbx_assets) > 10 or use_response_file:
        dbx_cook_errors = cook_all_dbx_assets(
            individual_dbx_assets, platform, cook_args, use_response_file
        )
    else:
        dbx_cook_errors = cook_dbx_assets_individually(individual_dbx_assets, platform, cook_args)

    if fail_on_dbx_cook and dbx_cook_errors:
        raise ELIPYException("Failed to cook individual dbx assets.")

    LOGGER.info("Done build individual assets")


def cook_dbx_assets_individually(
    dbx_assets: List[str], platform: str, cook_args: Dict
) -> List[Exception]:
    """
    Cook all assets individually.

    :param dbx_assets: A list of asset names to build.
    :param platform: What platform are we building for.
    :param cook_args: Arguments to pass to the cook command.
    :return: Returns a list of exceptions.
    """
    dbx_cook_errors = list()
    for dbx_asset in dbx_assets:
        dbx_cook_errors.extend(cook_all_dbx_assets([dbx_asset], platform, cook_args, False))
    return dbx_cook_errors


def cook_all_dbx_assets(
    dbx_assets: List[str], platform: str, cook_args: Dict, use_response_file: bool
) -> List[Exception]:
    """
    Cook all assets at the same time.

    :param dbx_assets: A list of asset names to build.
    :param platform: What platform are we building for.
    :param cook_args: Arguments to pass to the cook command.
    :return: Returns a list of exceptions.
    """
    dbx_cook_errors = list()
    reference_cook_args = copy.deepcopy(cook_args)
    reference_cook_args["use_response_file"] = use_response_file
    try:
        dbx_builder = data.DataUtils(platform, dbx_assets)
        dbx_builder.cook(**reference_cook_args)
    except Exception as exce:
        LOGGER.error(
            "Failure occurred when building dbx asset: {}.".format(dbx_assets),
            exc_info=True,
        )
        dbx_cook_errors.append(exce)
    return dbx_cook_errors


def validate_direct_dbx_references(
    dbx_assets: List[str], platform: str, cook_args: Dict, use_response_file: bool
) -> None:
    """
    Validate direct references for assets included in the shelved changelist.

    :param dbx_assets: A list of asset names to build.
    :param platform: What platform are we building for.
    :param cook_args: Arguments to pass to the cook command.
    :return: None (raises an exception in the case of failure).
    """
    reference_cook_args = copy.deepcopy(cook_args)
    reference_cook_args["use_response_file"] = use_response_file
    reference_cook_args["pipeline_args"] += [
        "-f",
        "validateDirectReferences",
        "-functionParameters",
        "\"{['LimitDirectRefErrorsToInput']=true}\"",
        "-Pipeline.MaxWarnings",
        "0",
    ]
    reference_builder = data.DataUtils(platform, dbx_assets)
    reference_builder.cook(**reference_cook_args)
