"""
deleter.py
"""

# pylint: disable=too-many-lines

import re
from collections import ChainMap
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.delete_utils import delete_empty_folders
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
import os
import glob
import json
from datetime import datetime
import multiprocessing
from multiprocessing.pool import ThreadPool
import six
import click
from typing import Any, Dict, Iterable, Optional, List

from azure.core.exceptions import ServiceRequestError as AzureServiceRequestError

from elipy2 import (
    expire,
    LOGGER,
    SETTINGS,
    avalanche,
    core,
    symbols,
    build_metadata_utils,
    az_utils,
    filer,
)
from elipy2.azcopy_client import AZCopyClient
from elipy2.exceptions import ELIPYException, ConfigValueNotFoundException
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("deleter")
@click.option(
    "--empty-folders",
    is_flag=True,
    help="Crawl project network path for empty folders and delete them.",
    default=False,
    required=False,
)
@click.option(
    "--dry-run",
    is_flag=True,
    help="Print the delete list without modifying it.",
    required=False,
)
@click.option(
    "--no-shift-delete",
    is_flag=True,
    help="Do not delete shift entries.",
    required=False,
)
@click.option("--avalanche-host", help="Host e.g. cg-buildrepo.eu.ad.ea.com", required=False)
@click.option("--clean-record", is_flag=True, help="Clean db records.", required=False)
@click.option(
    "--use-onefs-api",
    help="Delete using onefs_api method",
    is_flag=True,
    required=False,
)
@click.option(
    "--include-path-retention",
    help="Perform cleanup on the paths in the path_retention settings",
    is_flag=True,
    required=False,
)
@click.option(
    "--include-azure-path-retention",
    help="Perform cleanup on the paths in the azure_path_retention settings",
    is_flag=True,
    required=False,
)
@click.option(
    "--exclude-retention-categories",
    help="Do not perform cleanup on the retention_categories settings",
    is_flag=True,
    required=False,
)
@click.option(
    "--include",
    help="Include only these categories. By default all categories are included",
    multiple=True,
)
@click.option(
    "--exclude",
    help="Exclude these categories. Excludes take precedence over includes",
    multiple=True,
)
@click.option(
    "--clean-symstore-days",
    default=None,
    help="Clean up symstores files older than days",
)
@click.option(
    "--creation-time-deletion",
    default=False,
    is_flag=True,
    required=False,
    help="Clean up build dirs based on creation time",
)
@click.option(
    "--comprehensive-cleanup",
    default=False,
    is_flag=True,
    required=False,
    help="Use comprehensive 3-phase cleanup algorithm for all configured categories and branches",
)
@click.option(
    "--comprehensive-category",
    default=None,
    help="DEPRECATED: This parameter is ignored. Use --comprehensive-cleanup to process all categories.",
    hidden=True,
)
@click.option(
    "--comprehensive-branch",
    default=None,
    help="DEPRECATED: This parameter is ignored. Use --comprehensive-cleanup to process all branches.",
    hidden=True,
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    empty_folders,
    dry_run,
    no_shift_delete,
    avalanche_host,
    clean_record=False,
    use_onefs_api=False,
    include_path_retention=False,
    include_azure_path_retention=False,
    exclude_retention_categories=False,
    include=None,
    exclude=None,
    clean_symstore_days=None,
    creation_time_deletion=False,
    comprehensive_cleanup=False,
    comprehensive_category=None,
    comprehensive_branch=None,
):
    """
    foreach $path: retains $maxamount of items. retention map is expected in SETTINGS

    If invoked with `--dry-run`, print what would get deleted without modifying it
    """  # adding sentry tags
    add_sentry_tags(__file__)

    # Log warning if deprecated parameters are used
    if comprehensive_category is not None or comprehensive_branch is not None:
        LOGGER.warning(
            "DEPRECATED: --comprehensive-category and --comprehensive-branch parameters "
            "are ignored. Use --comprehensive-cleanup to process all configured "
            "categories and branches automatically."
        )

    LOGGER.info("performaing a --dry-run? {0}".format(dry_run))

    # Handle comprehensive cleanup mode
    if comprehensive_cleanup:
        LOGGER.info(
            "Running comprehensive 3-phase cleanup algorithm for all configured "
            "categories and branches"
        )

        try:
            release_candidates_to_keep_count = SETTINGS.get(
                "release_candidate_retention", default=56
            )
            retention_categories = SETTINGS.get("retention_categories")

            result = comprehensive_cleanup_builds(
                retention_categories=retention_categories,
                dry_run=dry_run,
                use_onefs_api=(SETTINGS.get("use_onefs_api") == "true"),
                release_candidates_to_keep_count=release_candidates_to_keep_count,
            )

            # Log summary results
            LOGGER.info("=== COMPREHENSIVE CLEANUP SUMMARY ===")
            LOGGER.info("Total categories processed: %d", result["total_categories_processed"])
            LOGGER.info("Total branches processed: %d", result["total_branches_processed"])
            LOGGER.info("Total builds found: %d", result["total_builds_found"])
            LOGGER.info("Deleted orphaned: %d", result["deleted_orphaned"])
            LOGGER.info("Deleted normal: %d", result["deleted_normal"])
            LOGGER.info("Deleted fallback: %d", result["deleted_fallback"])
            LOGGER.info("Protected RC: %d", result["protected_rc"])
            LOGGER.info("Total remaining builds: %d", result["total_remaining_builds"])

            if result["errors"]:
                LOGGER.warning("Errors encountered: %d", len(result["errors"]))
                for error in result["errors"]:
                    LOGGER.error("Comprehensive cleanup error: %s", error)
                raise ELIPYException("Comprehensive cleanup completed with errors.")

            LOGGER.info("Comprehensive cleanup completed successfully")

        except Exception as exc:
            LOGGER.error("Comprehensive cleanup failed: %s", exc)
            raise ELIPYException("Comprehensive cleanup failed: {0}".format(exc))

        return  # Exit early for comprehensive cleanup mode

    # Standard cleanup mode (existing logic)
    utils = expire.ExpireUtils()
    use_onefs_api = SETTINGS.get("use_onefs_api") == "true"
    exceptionlist = []
    exceptionlist += cleanup_shift(utils, no_shift_delete, dry_run, use_onefs_api)
    exceptionlist += cleanup_retention_paths(utils, include_path_retention, dry_run, use_onefs_api)
    if not exclude_retention_categories:
        exceptionlist += cleanup_builds(
            dry_run, use_onefs_api, include, exclude, creation_time_deletion
        )
    exceptionlist += cleanup_azure_retention_paths(include_azure_path_retention, dry_run)
    exceptionlist += cleanup_avalanche_records(avalanche_host, dry_run, clean_record)
    exceptionlist += cleanup_symstores(dry_run, clean_symstore_days)

    if empty_folders:
        exceptionlist += cleanup_empty_folders(dry_run)

    if exceptionlist:
        for i in exceptionlist:
            LOGGER.error(i, exc_info=True)
        raise ELIPYException("Failed to delete all files.")


def cleanup_empty_folders(dry_run):
    # type: (bool) -> List[Exception]
    """
    Crawls each retention category under build path and tries to remove empty-like folders.
    """
    exceptionlist = []

    categories = SETTINGS.get("retention_categories")
    for category in categories:
        category_path = os.path.join(SETTINGS.get("build_share"), category)
        LOGGER.debug(category_path)
        exceptionlist.extend(
            delete_empty_folders(
                dry_run=dry_run, files_equals_empty=["build.json"], path=category_path
            )
        )

    return exceptionlist


def cleanup_shift(utils, no_shift_delete, dry_run, use_onefs_api):
    "Cleanup shift builds"
    exceptionlist = []
    try:
        if not no_shift_delete:
            utils.keep_n_at_path(
                SETTINGS.get("shift_submission_path"),
                SETTINGS.get("shift_retention"),
                dry_run,
                use_onefs_api,
            )
    except Exception as exc:
        LOGGER.error(exc, exc_info=True)
        exceptionlist.append(exc)

    return exceptionlist


def cleanup_retention_paths(utils, include_path_retention, dry_run, use_onefs_api):
    "Cleanup retention paths"
    exceptionlist = []
    try:
        if include_path_retention:
            for each in SETTINGS.get("path_retention"):
                for path, maxamount in each.items():
                    if os.path.isdir(path):
                        LOGGER.info("retaining {0} at file:{1}".format(maxamount, path))
                        utils.keep_n_at_path(path, maxamount, dry_run, use_onefs_api)
                    else:
                        LOGGER.warning("Directory does not exist, skipping: {0}".format(path))
        else:
            LOGGER.info("Skipping path_retention files")
    except ConfigValueNotFoundException:
        pass
    except Exception as exc:
        LOGGER.error(exc, exc_info=True)
        exceptionlist.append(exc)

    return exceptionlist


def cleanup_azure_retention_paths(include_azure_path_retention, dry_run):
    """
    Cleanup Azure Fileshare retention paths
    @param include_azure_path_retention: skip if False
    @param dry_run: don't actually delete anything if True
    """
    exceptionlist = []
    # pylint: disable=too-many-nested-blocks
    try:
        if include_azure_path_retention:
            for az_storage_account in SETTINGS.get("azure_path_retention"):
                secret_context = az_storage_account.get("secret_context")
                for fileshare in az_storage_account.get("fileshares", []):
                    fileshare_name = fileshare.get("fileshare_name")
                    for path_item in fileshare.get("paths", []):
                        for fileshare_path, maxamount in path_item.items():
                            LOGGER.info(
                                "Deleting all but newest {} dirs at fileshare '{}' path {}".format(
                                    maxamount, fileshare_name, fileshare_path
                                )
                            )
                            LOGGER.info("maxamount is {}".format(maxamount))
                            keep_n_at_azure_path(
                                fileshare_path, maxamount, dry_run, secret_context, fileshare_name
                            )
        else:
            LOGGER.info("Skipping azure_fileshare_path_retention files")
    except ConfigValueNotFoundException:
        pass
    except Exception as exc:
        LOGGER.error(exc, exc_info=True)
        exceptionlist.append(exc)
    # pylint: enable=too-many-nested-blocks
    return exceptionlist


def keep_n_at_azure_path(
    fileshare_path,
    maxamount,
    dry_run,
    secret_context,
    fileshare_name,
    azure_domain="file.core.windows.net",
):
    """
    Delete all but the most recent n directories at the given path
    @param fileshare_path: path to the fileshare directory (in the form "Code/trunk-code-dev")
    @param maxamount: number of directories to keep
    @param dry_run: if True, print what would get deleted without modifying it
    @param secret_context: which secret in the elipy config to use.
    @param fileshare_name: name of the fileshare within the storage account
    """
    try:
        dirs_at_path_generator = az_utils.yield_azfileshare_path_contents_metadata(
            fileshare_path, secret_context=secret_context, share_name=fileshare_name
        )
        try:
            dirs_at_path = list(dirs_at_path_generator)

        except AzureServiceRequestError as exc:
            LOGGER.error(exc, exc_info=True)

            raise ELIPYException(
                "Failed to complete Azure request. Check that DNS resolution "
                "between the agent and the Azure resource is working or that the "
                "agent has an appropriate configuration in the hosts file "
                "(https://go.ea.com/slack_ref)"
            )

        # The name of the dir should be the CL number so sort only by digits in the name
        dirs_at_path.sort(
            key=lambda x: int(re.search(r"\d+", x["name"]).group())
        )  # lowest CL first

        azc_client = AZCopyClient(
            secret_context, share_name=fileshare_name, client_permissions="rdl"
        )

        directory_paths_to_delete = []
        for directory in dirs_at_path[: -int(maxamount)]:
            destination = "/".join(
                [
                    part.strip("/")
                    for part in [
                        f"https://{azc_client.account_name}.{azure_domain}",
                        fileshare_name,
                        fileshare_path,
                        directory["name"],
                    ]
                ]
            )

            directory_paths_to_delete.append(destination)

        LOGGER.info("directory_paths_to_delete: {}".format(directory_paths_to_delete))

        for destination in directory_paths_to_delete:
            if dry_run:
                LOGGER.info("Would delete folder {}".format(destination))
            else:
                LOGGER.info("Deleting folder {}".format(destination))
                azc_client.remove_dir(destination)

    except Exception as exc:
        LOGGER.error(exc, exc_info=True)
        raise ELIPYException(
            f"Failed to keep_n_at_azure_path {fileshare_path} with secret_context {secret_context}."
        )


def filter_categories(
    categories: Dict[str, Any],
    include: Optional[Iterable] = None,
    exclude: Optional[Iterable] = None,
):
    "Get a list of categories"
    include = include if include else []
    exclude = exclude if exclude else []

    selected_categories = {}

    invalid_includes = [k for k in include if k not in categories.keys()]
    invalid_excludes = [k for k in exclude if k not in categories.keys()]
    if invalid_includes or invalid_excludes:
        raise ValueError(
            "Invalid values passed for include ({0}) or exclude ({1})".format(
                invalid_includes, invalid_excludes
            )
        )

    included = {k: v for k, v in categories.items() if k in include}
    excluded = {k: v for k, v in categories.items() if k in exclude}
    if not included:
        included = categories

    selected_categories = {
        k: v for k, v in categories.items() if (k in included and k not in excluded)
    }

    return selected_categories


def cleanup_builds(
    dry_run, use_onefs_api, include=None, exclude=None, creation_time_deletion=False
):
    "cleanup builds"
    exceptionlist = []

    categories = SETTINGS.get("retention_categories")
    release_candidates_to_keep_count = SETTINGS.get("release_candidate_retention", default=56)
    selected_categories = filter_categories(categories, include, exclude)

    LOGGER.info("Selected categories are: %s", selected_categories)

    if creation_time_deletion:
        exc = delete_build_with_creation_time(dry_run, selected_categories)
        if exc:
            exceptionlist.append(exc)
    else:
        for sub_path, branch_list in selected_categories.items():
            branch_settings = dict(ChainMap(*branch_list))
            path = os.path.join(SETTINGS.get("build_share"), sub_path)
            branch_set = get_branch_set_under_path(path)
            clean_up_args = []

            for branch in branch_set:
                maxamount = branch_settings.get(branch, branch_settings["default"])
                LOGGER.info(
                    "retaining {0} at file:{1}".format(maxamount, os.path.join(path, branch))
                )
                clean_up_args.append(
                    (
                        os.path.join(sub_path, branch) + "\\",
                        maxamount,
                        dry_run,
                        use_onefs_api,
                        release_candidates_to_keep_count,
                    )
                )
            if clean_up_args != []:
                try:
                    pool = ThreadPool(processes=multiprocessing.cpu_count() - 1)
                    pool.map(unpack_expire, clean_up_args)
                    pool.close()
                    pool.join()
                except Exception as exc:
                    LOGGER.error(exc, exc_info=True)
                    exceptionlist.append(exc)
    return exceptionlist


def cleanup_avalanche_records(avalanche_host, dry_run, clean_record):
    "Cleanup avalanche records"
    exceptionlist = []
    if clean_record:
        try:
            check_and_drop_records(avalanche_host, dry_run)
        except Exception as exc:
            LOGGER.error(exc, exc_info=True)
            exceptionlist.append(exc)
    else:
        LOGGER.warning("Skipping Avalanche db deletion.")
    return exceptionlist


def cleanup_symstores(dry_run, cleanup_symstores_days):
    "Cleanup avalanche records"
    exceptionlist = []
    if cleanup_symstores_days:
        try:
            for platform in ["ps", "ms"]:
                symstore_path = symbols.SymbolsUtils().symbol_store_path.format(platform)
                core.run_age_store(symstore_path, cleanup_symstores_days, dry_run)
        except Exception as exc:
            LOGGER.error(exc, exc_info=True)
            exceptionlist.append(exc)
    else:
        LOGGER.warning("Skipping symstore cleanup.")
    return exceptionlist


def check_and_drop_records(avalanche_host, dry_run, percent_required_free=30):
    """
    Checks space left on central Avalanche store and delete oldest record and mark it in bilbo.
    """
    # Creating http + urls
    avalanche_url = "http://" + avalanche_host
    db_all_url = six.moves.urllib.parse.urljoin(avalanche_url, "/db/all")

    # Ping Avalanche for json/all
    try:
        with six.moves.urllib.request.urlopen(db_all_url) as response:
            data_read = response.read().decode("utf-8")
    except Exception as exc:
        LOGGER.warning("No Response from server {}".format(exc))
    else:
        data = json.loads(data_read)

        # Sort dates in reverse old so oldest is first in list.
        sorted_date = sorted(
            data, key=lambda x: datetime.strptime(x["updated"], "%Y-%m-%dT%H:%M:%SZ")
        )

        # Getting primary info from Avalanche.
        primary_info_url = six.moves.urllib.parse.urljoin(
            avalanche_url, "storage/pools/primary.json"
        )
        with six.moves.urllib.request.urlopen(primary_info_url) as response:
            data_read = response.read()
        data = json.loads(data_read)
        diskpercent = (
            float(data["Primary"]["pools"][0]["freeDiskSpace"])
            / float(data["Primary"]["pools"][0]["totalDiskSpace"])
            * 100
        )
        percent_left = avalanche.get_avalanche_free_space(0, avalanche_url)

        if (percent_left < percent_required_free) or (diskpercent < percent_required_free):
            for item in sorted_date:
                LOGGER.info(item["id"] + " : " + item["updated"])

            set_range = 1
            if not dry_run:
                if diskpercent < percent_required_free:
                    set_range = 9

                # Displaying Build to be Deleted
                LOGGER.warning("Less than {0} percent free HDD space left".format(diskpercent))

                if sorted_date:
                    LOGGER.info("The following record(s) will be deleted:")
                    for i in range(set_range):
                        record_to_delete = sorted_date[i]
                        LOGGER.info(record_to_delete["id"] + " : " + record_to_delete["updated"])
                        avalanche.drop_build_record(record_to_delete["id"], avalanche_url)
                        reverse_bilbo_delete(dry_run, record_to_delete, avalanche_host)
        else:
            LOGGER.info("Space avaliable more than minimum setting: {}".format(percent_left))
            LOGGER.info("Skipping deletion.")


def reverse_bilbo_delete(dry_run, record_to_delete, avalanche_host):
    """
    Removes bilbo record by doing a reverse platform lookup.
    """
    metadata_manager = build_metadata_utils.setup_metadata_manager()

    split_list = record_to_delete["id"].split(".")
    for platform in avalanche.get_reverse_avalanche_platform(split_list[2]):
        # Bilbo Path = Clone_Host, Destination_DB, Platform,
        # Data_branch, Data_CL, Code_Brance, Code_CL.
        bilbo_path = (
            avalanche_host
            + "."
            + record_to_delete["id"]
            + "."
            + platform
            + "."
            + split_list[1]
            + "."
            + split_list[3]
            + "."
            + split_list[1]
            + "."
            + split_list[4]
        )

        LOGGER.info("Getting Bilbo Path: {}".format(bilbo_path))
        if not dry_run:
            metadata_manager.delete_build(bilbo_path)


def get_branch_set_under_path(path):
    """
    Checks bilbo for all path containing "path" and parses out the branch names
    branch names are returned in a set
    """
    metadata_manager = build_metadata_utils.setup_metadata_manager()

    builds = metadata_manager.get_builds_matching(path)
    builds = metadata_manager.get_build_ids(builds)
    branch_set = set()
    for build in builds:
        for index, item in enumerate(build.split("\\")):
            if item.lower() == path.split("\\")[-1].lower():
                branch_set.add(build.split("\\")[index + 1])
    return branch_set


def delete_build_with_creation_time(dry_run, selected_categories):
    """
    Cleanup builds based on creation time
    """
    metadata_manager = build_metadata_utils.setup_metadata_manager()
    exceptionlist = []
    for sub_path, branch_list in selected_categories.items():
        branch_settings = dict(ChainMap(*branch_list))
        path = os.path.join(SETTINGS.get("build_share"), sub_path)
        for branch in branch_settings:
            maxamount = branch_settings.get(branch, branch_settings["default"])
            delete_path = os.path.join(path, branch)
            LOGGER.info("retaining {0} at file:{1}".format(maxamount, delete_path))
            try:
                dirs = list(filter(os.path.isdir, glob.glob(delete_path + "\\*")))
                dirs.sort(key=os.path.getctime, reverse=True)
                deleting_dirs = dirs[maxamount:]
                LOGGER.info("Builds will be deleted {0}".format(deleting_dirs))
                if not dry_run and deleting_dirs:
                    for build in deleting_dirs:
                        core.delete_filer_folder(build, 1)
                        LOGGER.info("marking as deleted in bilbo:   %s", build)
                        metadata_manager.delete_build(build)
            except Exception as exc:
                LOGGER.error(exc, exc_info=True)
                exceptionlist.append(exc)
    return exceptionlist


def unpack_expire(args):
    """
    Helper function to unpack arguments from pool map function
    """
    utils = expire.ExpireUtils()
    utils.expire(*args)


# ============================================================================
# COMPREHENSIVE BUILD CLEANUP ALGORITHM - 3-PHASE APPROACH
# ============================================================================


def extract_cl_from_directory_name(directory_name):
    """
    Extract changelist number from directory name.
    Expected format: CL-12345 or similar patterns
    Returns: int or None if no CL found
    """
    cl_match = re.search(r"CL-(\d+)", directory_name, re.IGNORECASE)
    if cl_match:
        return int(cl_match.group(1))

    # Fallback: try to extract any number from the directory name
    number_match = re.search(r"(\d+)", directory_name)
    if number_match:
        return int(number_match.group(1))

    return None


def scan_physical_directories(branch_path):
    r"""
    Phase 1: Scan physical build directories and extract CL information.

    Args:
        branch_path: Full path to branch directory (e.g., \\filer\builds\fb1\code\branch_name)

    Returns:
        dict: {
            'total_builds': int,
            'builds_by_cl': {cl_number: {'path': full_path, 'creation_time': timestamp}},
            'sorted_cls': [cl1, cl2, cl3, ...] (ascending order, oldest to newest)
        }
    """
    result = {"total_builds": 0, "builds_by_cl": {}, "sorted_cls": []}

    if not os.path.exists(branch_path):
        LOGGER.warning("Branch path does not exist: %s", branch_path)
        return result

    try:
        # Get all directories in the branch path
        dirs = [d for d in os.listdir(branch_path) if os.path.isdir(os.path.join(branch_path, d))]

        result["total_builds"] = len(dirs)

        for directory in dirs:
            full_path = os.path.join(branch_path, directory)
            cl_number = extract_cl_from_directory_name(directory)

            if cl_number is not None:
                creation_time = os.path.getctime(full_path)
                result["builds_by_cl"][cl_number] = {
                    "path": full_path,
                    "creation_time": creation_time,
                    "directory_name": directory,
                }

        # Sort CLs in ascending order (oldest to newest)
        result["sorted_cls"] = sorted(result["builds_by_cl"].keys())

        LOGGER.info(
            "Scanned %d directories in %s, found %d with valid CL numbers",
            result["total_builds"],
            branch_path,
            len(result["sorted_cls"]),
        )

    except Exception as exc:
        LOGGER.error("Error scanning directories in %s: %s", branch_path, exc)

    return result


def parse_retention_policy_for_branch(category, branch):
    """
    Parse retention policy from YAML config for specific category/branch.

    Args:
        category: Category name (e.g., 'code', 'frosty\\\\walrus')
        branch: Branch name (e.g., 'dev-na-dice-next-build')

    Returns:
        int: Retention count for this branch
    """
    try:
        categories = SETTINGS.get("retention_categories")
        if category not in categories:
            LOGGER.warning("Category %s not found in retention_categories", category)
            return 0

        branch_list = categories[category]
        branch_settings = dict(ChainMap(*branch_list))

        # Get retention for specific branch or default
        retention_count = branch_settings.get(branch, branch_settings.get("default", 0))

        LOGGER.debug("Retention policy for %s/%s: %d builds", category, branch, retention_count)
        return retention_count

    except Exception as exc:
        LOGGER.error("Error parsing retention policy for %s/%s: %s", category, branch, exc)
        return 0


def query_bilbo_build_status(build_path, metadata_manager):
    """
    Phase 2: Query Bilbo for build deletion status and release candidate status.

    Args:
        build_path: Full path to build directory
        metadata_manager: Bilbo metadata manager instance

    Returns:
        dict: {
            'exists_in_bilbo': bool,
            'is_deleted': bool,
            'is_release_candidate': bool,
            'build_metadata': dict or None
        }
    """
    result = {
        "exists_in_bilbo": False,
        "is_deleted": False,
        "is_release_candidate": False,
        "build_metadata": None,
    }

    try:
        # Query Bilbo for builds matching this path
        builds = metadata_manager.get_builds_matching(build_path)

        if builds:
            result["exists_in_bilbo"] = True
            # Take the first matching build
            build = builds[0]
            result["build_metadata"] = build.source if hasattr(build, "source") else {}

            # Check if marked as deleted in Bilbo
            if hasattr(build, "source") and build.source:
                result["is_deleted"] = "deleted" in build.source

                # Check if it's a release candidate
                release_type = build.source.get("release_type")
                result["is_release_candidate"] = release_type == "release_candidate"

        LOGGER.debug(
            "Bilbo status for %s: exists=%s, deleted=%s, RC=%s",
            build_path,
            result["exists_in_bilbo"],
            result["is_deleted"],
            result["is_release_candidate"],
        )

    except Exception as exc:
        LOGGER.error("Error querying Bilbo for %s: %s", build_path, exc)

    return result


def apply_retention_policy(directory_scan, retention_count):
    """
    Phase 1: Apply retention policy to create deletion candidates and protected builds.

    Args:
        directory_scan: Result from scan_physical_directories()
        retention_count: Number of builds to retain

    Returns:
        dict: {
            'deletion_candidates': [cl_numbers...] (oldest builds beyond retention limit),
            'protected_builds': [cl_numbers...] (newest builds within retention limit),
            'excess_count': int (number of builds beyond retention limit)
        }
    """
    sorted_cls = directory_scan["sorted_cls"]
    total_builds = len(sorted_cls)

    result = {"deletion_candidates": [], "protected_builds": [], "excess_count": 0}

    if total_builds <= retention_count:
        # All builds are protected
        result["protected_builds"] = sorted_cls[:]
        LOGGER.info("All %d builds are within retention limit of %d", total_builds, retention_count)
    else:
        # Split into deletion candidates (oldest) and protected (newest)
        excess_count = total_builds - retention_count
        result["deletion_candidates"] = sorted_cls[:excess_count]  # Oldest builds
        result["protected_builds"] = sorted_cls[excess_count:]  # Newest builds
        result["excess_count"] = excess_count

        LOGGER.info(
            "Retention policy: %d total builds, keeping %d newest, %d candidates for deletion",
            total_builds,
            retention_count,
            excess_count,
        )

    return result


def _delete_orphaned_build(build_path, use_onefs_api, dry_run):
    """
    Helper function to delete orphaned builds (not in Bilbo or marked as deleted).

    Returns:
        bool: True if deletion was successful or would be successful in dry run
    """
    try:
        if not dry_run:
            if use_onefs_api:
                filer.FilerUtils.delete_with_onefs_api(build_path)
            else:
                core.delete_filer_folder(build_path, 1)
            LOGGER.info("Deleted orphaned build: %s", build_path)
        else:
            LOGGER.info("Would delete orphaned build: %s", build_path)
        return True
    except Exception as exc:
        LOGGER.error("Failed to delete orphaned build %s: %s", build_path, exc)
        return False


def _delete_normal_build(build_path, use_onefs_api, dry_run, metadata_manager):
    """
    Helper function to delete normal builds and mark them in Bilbo.

    Returns:
        bool: True if deletion was successful or would be successful in dry run
    """
    try:
        if not dry_run:
            if use_onefs_api:
                filer.FilerUtils.delete_with_onefs_api(build_path)
            else:
                core.delete_filer_folder(build_path, 1)
            LOGGER.info("Marking as deleted in Bilbo: %s", build_path)
            metadata_manager.delete_build(build_path)
            LOGGER.info("Deleted normal build: %s", build_path)
        else:
            LOGGER.info("Would delete normal build: %s", build_path)
        return True
    except Exception as exc:
        LOGGER.error("Failed to delete normal build %s: %s", build_path, exc)
        return False


def _delete_fallback_build(build_path, use_onefs_api, dry_run, metadata_manager):
    """
    Helper function to delete fallback builds and mark them in Bilbo.

    Returns:
        bool: True if deletion was successful or would be successful in dry run
    """
    try:
        if not dry_run:
            if use_onefs_api:
                filer.FilerUtils.delete_with_onefs_api(build_path)
            else:
                core.delete_filer_folder(build_path, 1)
            LOGGER.info("Marking as deleted in Bilbo: %s", build_path)
            metadata_manager.delete_build(build_path)
            LOGGER.info("Deleted fallback build: %s", build_path)
        else:
            LOGGER.info("Would delete fallback build: %s", build_path)
        return True
    except Exception as exc:
        LOGGER.error("Failed to delete fallback build %s: %s", build_path, exc)
        return False


def _process_single_category_branch(
    category,
    branch,
    retention_count,
    metadata_manager,
    build_share,
    dry_run,
    use_onefs_api,
    release_candidates_to_keep_count,  # pylint: disable=unused-argument
):
    """
    Process a single category-branch combination using the comprehensive 3-phase algorithm.

    Returns:
        dict: Results for this specific category-branch combination
    """
    # Initialize result structure for this branch
    result = {
        "branch": branch,
        "category": category,
        "total_builds_found": 0,
        "retention_policy": retention_count,
        "deleted_orphaned": 0,
        "deleted_normal": 0,
        "deleted_fallback": 0,
        "protected_rc": 0,
        "remaining_builds": [],
        "errors": [],
    }

    try:
        branch_path = os.path.join(build_share, category, branch)

        # PHASE 1: Directory Analysis and Retention Policy
        LOGGER.info("Phase 1: Scanning physical directories and applying retention policy")
        directory_scan = scan_physical_directories(branch_path)
        result["total_builds_found"] = directory_scan["total_builds"]

        if result["total_builds_found"] == 0:
            LOGGER.info("No builds found in %s", branch_path)
            return result

        if retention_count == 0:
            LOGGER.warning("Retention policy is 0 for %s/%s - skipping cleanup", category, branch)
            result["remaining_builds"] = directory_scan["sorted_cls"]
            return result

        # Apply retention policy
        retention_result = apply_retention_policy(directory_scan, retention_count)

        if not retention_result["deletion_candidates"]:
            LOGGER.info("No builds exceed retention limit - nothing to delete")
            result["remaining_builds"] = directory_scan["sorted_cls"]
            return result

        LOGGER.info(
            "Phase 1 complete: %d deletion candidates, %d protected builds",
            len(retention_result["deletion_candidates"]),
            len(retention_result["protected_builds"]),
        )

        # PHASE 2: Bilbo Verification and Deletion Logic
        LOGGER.info("Phase 2: Bilbo verification and deletion logic")

        rc_protected_count = 0

        for cl_number in retention_result["deletion_candidates"]:
            build_info = directory_scan["builds_by_cl"][cl_number]
            build_path = build_info["path"]

            # Query Bilbo status
            bilbo_status = query_bilbo_build_status(build_path, metadata_manager)

            if not bilbo_status["exists_in_bilbo"]:
                # Build not found in Bilbo - treat as orphaned
                LOGGER.info("Orphaned build (not in Bilbo): %s", build_path)
                if _delete_orphaned_build(build_path, use_onefs_api, dry_run):
                    result["deleted_orphaned"] += 1
                else:
                    result["errors"].append(f"Failed to delete orphaned build: {build_path}")

            elif bilbo_status["is_deleted"]:
                # Build marked as deleted in Bilbo but still on disk
                LOGGER.info("Orphaned build (marked deleted in Bilbo): %s", build_path)
                if _delete_orphaned_build(build_path, use_onefs_api, dry_run):
                    result["deleted_orphaned"] += 1
                else:
                    result["errors"].append(f"Failed to delete orphaned build: {build_path}")

            elif bilbo_status["is_release_candidate"]:
                # Release candidate - skip deletion and increment RC protected count
                LOGGER.info("Protecting release candidate build: %s", build_path)
                rc_protected_count += 1
                result["protected_rc"] += 1

            else:
                # Normal build - delete it
                LOGGER.info("Normal deletion candidate: %s", build_path)
                if _delete_normal_build(build_path, use_onefs_api, dry_run, metadata_manager):
                    result["deleted_normal"] += 1
                else:
                    result["errors"].append(f"Failed to delete normal build: {build_path}")

        LOGGER.info(
            "Phase 2 complete: %d orphaned deleted, %d normal deleted, %d RC protected",
            result["deleted_orphaned"],
            result["deleted_normal"],
            result["protected_rc"],
        )

        # PHASE 3: Fallback Cleanup (Creation Time Deletion)
        if rc_protected_count > 0:
            LOGGER.info("Phase 3: Fallback cleanup - RC builds prevented normal cleanup")
            LOGGER.info(
                "Need to delete %d additional builds using creation time", rc_protected_count
            )

            # Get current protected builds and sort by creation time (oldest first)
            protected_builds_with_time = []
            for cl_number in retention_result["protected_builds"]:
                if cl_number in directory_scan["builds_by_cl"]:
                    build_info = directory_scan["builds_by_cl"][cl_number]
                    protected_builds_with_time.append((cl_number, build_info))

            # Sort by creation time (oldest first)
            protected_builds_with_time.sort(key=lambda x: x[1]["creation_time"])

            fallback_deleted = 0
            for cl_number, build_info in protected_builds_with_time:
                if fallback_deleted >= rc_protected_count:
                    break

                build_path = build_info["path"]

                # Check if this build is also an RC (don't delete RC builds even in fallback)
                bilbo_status = query_bilbo_build_status(build_path, metadata_manager)
                if bilbo_status["is_release_candidate"]:
                    LOGGER.info("Skipping RC build in fallback cleanup: %s", build_path)
                    continue

                # Delete this build
                LOGGER.info("Fallback deletion (creation time): %s", build_path)
                if _delete_fallback_build(build_path, use_onefs_api, dry_run, metadata_manager):
                    result["deleted_fallback"] += 1
                    fallback_deleted += 1
                else:
                    result["errors"].append(f"Failed to delete fallback build: {build_path}")

            LOGGER.info("Phase 3 complete: %d fallback builds deleted", result["deleted_fallback"])
        else:
            LOGGER.info("Phase 3: No fallback cleanup needed")

        # Rescan to get actual remaining builds
        final_scan = scan_physical_directories(branch_path)
        result["remaining_builds"] = final_scan["sorted_cls"]

        LOGGER.info("=== CLEANUP COMPLETE FOR %s/%s ===", category, branch)
        LOGGER.info("Total builds found: %d", result["total_builds_found"])
        LOGGER.info("Retention policy: %d", result["retention_policy"])
        LOGGER.info("Deleted orphaned: %d", result["deleted_orphaned"])
        LOGGER.info("Deleted normal: %d", result["deleted_normal"])
        LOGGER.info("Deleted fallback: %d", result["deleted_fallback"])
        LOGGER.info("Protected RC: %d", result["protected_rc"])
        LOGGER.info("Remaining builds: %d", len(result["remaining_builds"]))

    except Exception as exc:
        LOGGER.error("Error in comprehensive cleanup for %s/%s: %s", category, branch, exc)
        result["errors"].append(str(exc))

    return result


def comprehensive_cleanup_builds(
    retention_categories,
    dry_run=True,
    use_onefs_api=False,
    release_candidates_to_keep_count=56,
):
    """
    Comprehensive 3-phase build cleanup algorithm for all configured categories and branches.

    Phase 1: Directory Analysis and Retention Policy
    Phase 2: Bilbo Verification and Deletion Logic
    Phase 3: Fallback Cleanup (Creation Time Deletion)

    Args:
        retention_categories: Complete retention_categories configuration from SETTINGS
        dry_run: If True, only log what would be deleted
        use_onefs_api: Use OneFS API for deletion
        release_candidates_to_keep_count: Max RC builds to protect

    Returns:
        dict: Aggregated cleanup results across all categories and branches
    """
    LOGGER.info("=== COMPREHENSIVE BUILD CLEANUP: ALL CATEGORIES AND BRANCHES ===")

    # Initialize aggregated result structure
    result = {
        "total_categories_processed": 0,
        "total_branches_processed": 0,
        "total_builds_found": 0,
        "deleted_orphaned": 0,
        "deleted_normal": 0,
        "deleted_fallback": 0,
        "protected_rc": 0,
        "total_remaining_builds": 0,
        "errors": [],
        "category_results": [],  # Detailed results per category-branch
    }

    try:
        # Setup
        metadata_manager = build_metadata_utils.setup_metadata_manager()
        build_share = SETTINGS.get("build_share")

        # Process each category in retention_categories
        for category, branch_list in retention_categories.items():
            LOGGER.info("=== Processing category: %s ===", category)
            result["total_categories_processed"] += 1

            # Get branch settings for this category
            branch_settings = dict(ChainMap(*branch_list))
            category_path = os.path.join(build_share, category)

            # Get actual branches that exist for this category
            branch_set = get_branch_set_under_path(category_path)

            if not branch_set:
                LOGGER.warning("No branches found for category: %s", category)
                continue

            # Process each branch in this category
            for branch in branch_set:
                LOGGER.info("=== Processing branch: %s/%s ===", category, branch)
                result["total_branches_processed"] += 1

                # Get retention policy for this branch
                retention_count = branch_settings.get(branch, branch_settings.get("default", 0))

                if retention_count == 0:
                    LOGGER.warning(
                        "Retention policy is 0 for %s/%s - skipping cleanup", category, branch
                    )
                    continue

                # Process this specific category-branch combination
                branch_result = _process_single_category_branch(
                    category=category,
                    branch=branch,
                    retention_count=retention_count,
                    metadata_manager=metadata_manager,
                    build_share=build_share,
                    dry_run=dry_run,
                    use_onefs_api=use_onefs_api,
                    release_candidates_to_keep_count=release_candidates_to_keep_count,
                )

                # Aggregate results
                result["total_builds_found"] += branch_result["total_builds_found"]
                result["deleted_orphaned"] += branch_result["deleted_orphaned"]
                result["deleted_normal"] += branch_result["deleted_normal"]
                result["deleted_fallback"] += branch_result["deleted_fallback"]
                result["protected_rc"] += branch_result["protected_rc"]
                result["total_remaining_builds"] += len(branch_result["remaining_builds"])
                result["errors"].extend(branch_result["errors"])
                result["category_results"].append(branch_result)

        LOGGER.info("=== COMPREHENSIVE CLEANUP COMPLETE ===")
        LOGGER.info("Total categories processed: %d", result["total_categories_processed"])
        LOGGER.info("Total branches processed: %d", result["total_branches_processed"])
        LOGGER.info("Total builds found: %d", result["total_builds_found"])
        LOGGER.info("Deleted orphaned: %d", result["deleted_orphaned"])
        LOGGER.info("Deleted normal: %d", result["deleted_normal"])
        LOGGER.info("Deleted fallback: %d", result["deleted_fallback"])
        LOGGER.info("Protected RC: %d", result["protected_rc"])
        LOGGER.info("Total remaining builds: %d", result["total_remaining_builds"])

    except Exception as exc:
        LOGGER.error("Error in comprehensive cleanup: %s", exc)
        result["errors"].append(str(exc))

    return result
