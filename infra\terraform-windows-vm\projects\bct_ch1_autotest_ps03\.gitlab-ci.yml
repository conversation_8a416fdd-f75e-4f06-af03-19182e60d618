#**********************************************
#               bct_ch1_autotest_ps03 PIPE              *
#**********************************************
.default-bct-ch1-autotest-ps03-variables:
  extends: .secrets-bct_ch1_autotest_ps03
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ch1_autotest_ps03"
    WORKING_DIR: "projects/bct_ch1_autotest_ps03"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-bct-ch1-autotest-ps03.json"
    ansible_main_module: bct_ch1_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_ch1_PS03.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log
    ANSIBLE_FORKS: 200

prepare-json-config-bct-ch1-autotest-ps03:
  extends: ['.default-bct-ch1-autotest-ps03-variables', '.prepare_config']

validate-bct-ch1-autotest-ps03:
  extends: ['.default-bct-ch1-autotest-ps03-variables', '.validation_steps']

plan-bct-ch1-autotest-ps03:
  needs:
    - job: validate-bct-ch1-autotest-ps03
    - job: prepare-json-config-bct-ch1-autotest-ps03
  extends: ['.default-bct-ch1-autotest-ps03-variables', '.plan_steps']

apply-bct-ch1-autotest-ps03:
  needs:
    - job: plan-bct-ch1-autotest-ps03
    - job: prepare-json-config-bct-ch1-autotest-ps03
  extends: ['.default-bct-ch1-autotest-ps03-variables', '.apply_steps']

attache-bct-ch1-autotest-ps03:
  needs:
    - job: apply-bct-ch1-autotest-ps03
    - job: prepare-json-config-bct-ch1-autotest-ps03
  extends: ['.default-bct-ch1-autotest-ps03-variables', '.attache_vmdk_step']

sync-bct-ch1-autotest-ps03:
  needs:
    - job: apply-bct-ch1-autotest-ps03
    - job: attache-bct-ch1-autotest-ps03
    - job: prepare-json-config-bct-ch1-autotest-ps03
  extends: ['.default-bct-ch1-autotest-ps03-variables', '.sync_vmdk_step']

ansible-bct-ch1-autotest-ps03:
  needs:
    - job: apply-bct-ch1-autotest-ps03
    - job: sync-bct-ch1-autotest-ps03
    - job: prepare-json-config-bct-ch1-autotest-ps03
  extends: ['.default-bct-ch1-autotest-ps03-variables', '.run_ansible_step']

