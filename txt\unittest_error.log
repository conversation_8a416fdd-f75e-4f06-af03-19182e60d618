=================================== FAILURES ===================================
_ TestUpdatedCLIInterface.test_comprehensive_cleanup_flag_works_without_parameters _
self = <dice_elipy_scripts.tests.test_deleter.TestUpdatedCLIInterface object at 0x7fb985597e90>
mock_settings = <MagicMock name='SETTINGS' id='140434740619728'>
mock_comprehensive_cleanup = <MagicMock name='comprehensive_cleanup_builds' id='140434752435664'>
args = (<MagicMock name='cleanup_symstores' id='140434738478864'>, <MagicMock name='cleanup_azure_retention_paths' id='140434...<MagicMock name='cleanup_retention_paths' id='140434740096080'>, <MagicMock name='cleanup_shift' id='140434738689040'>)
runner = <click.testing.CliRunner object at 0x7fb982ca1c10>
result = <Result okay>, @py_assert1 = None
    @patch("dice_elipy_scripts.deleter.cleanup_shift", return_value=[])
    @patch("dice_elipy_scripts.deleter.cleanup_retention_paths", return_value=[])
    @patch("dice_elipy_scripts.deleter.cleanup_builds", return_value=[])
    @patch("dice_elipy_scripts.deleter.cleanup_avalanche_records", return_value=[])
    @patch("dice_elipy_scripts.deleter.cleanup_azure_retention_paths", return_value=[])
    @patch("dice_elipy_scripts.deleter.cleanup_symstores", return_value=[])
    @patch("dice_elipy_scripts.deleter.comprehensive_cleanup_builds")
    @patch("dice_elipy_scripts.deleter.SETTINGS")
    def test_comprehensive_cleanup_flag_works_without_parameters(
        self, mock_settings, mock_comprehensive_cleanup, *args
    ):
        """Test that --comprehensive-cleanup flag works without requiring category/branch parameters"""
        # Setup mock settings
        mock_settings.get.side_effect = lambda key, default=None: {
            "use_onefs_api": "false",
            "release_candidate_retention": 56,
            "retention_categories": {
                "code": [{"default": 50}],
                "frosty\\walrus": [{"default": 30}],
            },
        }.get(key, default)

        # Mock comprehensive cleanup to return success
        mock_comprehensive_cleanup.return_value = {
            "total_categories_processed": 2,
            "total_branches_processed": 2,
            "total_builds_found": 10,
            "deleted_orphaned": 1,
            "deleted_normal": 2,
            "deleted_fallback": 0,
            "protected_rc": 1,
            "total_remaining_builds": 6,
            "errors": [],
        }

        # Run CLI with comprehensive cleanup flag only
        runner = CliRunner()
        result = runner.invoke(cli, ["--comprehensive-cleanup", "--dry-run"])

        # Should succeed without requiring additional parameters
        assert result.exit_code == 0
        mock_comprehensive_cleanup.assert_called_once()  # Verify the function was called with retention_categories
        call_args = mock_comprehensive_cleanup.call_args

        # Extract keyword arguments from the mock call
        call_kwargs = call_args.kwargs if call_args else {}

        # Check if retention_categories was passed correctly
        expected_retention_categories = {
            "code": [{"default": 50}],
            "frosty\\walrus": [{"default": 30}],
        }

>       assert (
            "retention_categories" in call_kwargs
        ), f"retention_categories not found in call kwargs. call_args: {call_args}"
E       AssertionError: retention_categories not found in call kwargs. call_args: call(dry_run=True, release_candidates_to_keep_count=56, retention_categories={'code': [{'default': 50}], 'frosty\\walrus': [{'default': 30}]}, use_onefs_api=False)
E       assert 'retention_categories' in kwargs
dice_elipy_scripts/tests/test_deleter.py:1145: AssertionError
----------------------------- Captured stdout call -----------------------------
2025-06-16 07:49:06 elipy2 [WARNING]:
Can not find file: tnt_root/bin/FrostbiteRelease.txt.
Assuming a higher version with new file, please update the file check.

2025-06-16 07:49:06 elipy2 [INFO]: performaing a --dry-run? True
2025-06-16 07:49:06 elipy2 [INFO]: Running comprehensive 3-phase cleanup algorithm for all configured categories and branches
2025-06-16 07:49:06 elipy2 [INFO]: === COMPREHENSIVE CLEANUP SUMMARY ===
2025-06-16 07:49:06 elipy2 [INFO]: Total categories processed: 2
2025-06-16 07:49:06 elipy2 [INFO]: Total branches processed: 2
2025-06-16 07:49:06 elipy2 [INFO]: Total builds found: 10
2025-06-16 07:49:06 elipy2 [INFO]: Deleted orphaned: 1
2025-06-16 07:49:06 elipy2 [INFO]: Deleted normal: 2
2025-06-16 07:49:06 elipy2 [INFO]: Deleted fallback: 0
2025-06-16 07:49:06 elipy2 [INFO]: Protected RC: 1
2025-06-16 07:49:06 elipy2 [INFO]: Total remaining builds: 6
2025-06-16 07:49:06 elipy2 [INFO]: Comprehensive cleanup completed successfully
_ TestComprehensiveCleanupIntegration.test_comprehensive_cleanup_dry_run_mode_integration _
self = <dice_elipy_scripts.tests.test_deleter.TestComprehensiveCleanupIntegration object at 0x7fb985123210>
    def test_comprehensive_cleanup_dry_run_mode_integration(self):
        """Test that dry run mode is properly passed through the entire flow"""
        with patch("dice_elipy_scripts.deleter.build_metadata_utils.setup_metadata_manager"):
            with patch("dice_elipy_scripts.deleter.SETTINGS") as mock_settings:
                with patch(
                    "dice_elipy_scripts.deleter.get_branch_set_under_path"
                ) as mock_get_branch_set:
                    with patch(
                        "dice_elipy_scripts.deleter._process_single_category_branch"
                    ) as mock_process:
                        mock_settings.get.side_effect = lambda key, default=None: {
                            "build_share": "\\\\build\\share",
                            "retention_categories": {"code": [{"default": 5}]},
                        }.get(key, default)

                        mock_get_branch_set.return_value = {"main"}
                        mock_process.return_value = {
                            "branch": "main",
                            "category": "code",
                            "total_builds_found": 0,
                            "deleted_orphaned": 0,
                            "deleted_normal": 0,
                            "deleted_fallback": 0,
                            "protected_rc": 0,
                            "remaining_builds": [],
                            "errors": [],
                        }

                        # Test dry run mode
                        comprehensive_cleanup_builds(
                            retention_categories={"code": [{"default": 5}]},
                            dry_run=True,
                        )  # Verify dry_run=True was passed to the helper function
                        mock_process.assert_called_once()
                        call_args = mock_process.call_args

                        # Extract keyword arguments from the mock call
                        call_kwargs = call_args.kwargs if call_args else {}

                        # Check if dry_run=True was passed correctly
>                       assert (
                            "dry_run" in call_kwargs
                        ), f"dry_run not found in call kwargs. call_args: {call_args}"
E                       AssertionError: dry_run not found in call kwargs. call_args: call(branch='main', build_share='\\\\build\\share', category='code', dry_run=True, metadata_manager=<MagicMock name='setup_metadata_manager()' id='140434738684240'>, release_candidates_to_keep_count=56, retention_count=5, use_onefs_api=False)
E                       assert 'dry_run' in kwargs
dice_elipy_scripts/tests/test_deleter.py:1866: AssertionError
----------------------------- Captured stdout call -----------------------------
2025-06-16 07:49:06 elipy2 [INFO]: === COMPREHENSIVE BUILD CLEANUP: ALL CATEGORIES AND BRANCHES ===
2025-06-16 07:49:06 elipy2 [INFO]: === Processing category: code ===
2025-06-16 07:49:06 elipy2 [INFO]: === Processing branch: code/main ===
2025-06-16 07:49:06 elipy2 [INFO]: === COMPREHENSIVE CLEANUP COMPLETE ===
2025-06-16 07:49:06 elipy2 [INFO]: Total categories processed: 1
2025-06-16 07:49:06 elipy2 [INFO]: Total branches processed: 1
2025-06-16 07:49:06 elipy2 [INFO]: Total builds found: 0
2025-06-16 07:49:06 elipy2 [INFO]: Deleted orphaned: 0
2025-06-16 07:49:06 elipy2 [INFO]: Deleted normal: 0
2025-06-16 07:49:06 elipy2 [INFO]: Deleted fallback: 0
2025-06-16 07:49:06 elipy2 [INFO]: Protected RC: 0
2025-06-16 07:49:06 elipy2 [INFO]: Total remaining builds: 0