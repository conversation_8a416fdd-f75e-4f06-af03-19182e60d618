"""
preflight_utils.py
"""
from elipy2.exceptions import ELIPYException
from elipy2.p4 import P4Utils
from elipy2 import LOGGER


def raise_if_cl_not_exists(perforce: P4Utils, changelist: str) -> None:
    """Raise exception if changelist doesn't exist

    Raise exception if changelist doesn't exist
    Parameters
    ----------
    perforce : P4Utils
        Perforce object to run commands against
    changelist : str
        The changelist we should be checking for

    Returns
    -------
    None
    """
    describe_output = perforce.describe(changelist, show_shelved_files=True)
    if describe_output[0][b"code"] == b"error":
        error = describe_output[0][b"data"]
        LOGGER.info("Perforce describe error: %s", error)
        if b"tcp receive exceeded maximum configured duration of" in error.lower():
            raise ELIPYException(
                "Perforce timed out trying to run 'p4 describe'. Please retry your build."
            )
        raise ELIPYException(f"Changelist does not exist: {changelist}")

    LOGGER.info("Changelist found!")


def raise_if_wrong_stream(perforce: P4Utils, changelist: str) -> None:
    """Raise exception if the shelved files are in a different stream

    Raise exception if the shelved files are in a different stream
    Parameters
    ----------
    perforce : P4Utils
        Perforce object to run commands against
    changelist : str
        The changelist we should be checking for

    Returns
    -------
    None
    """
    describe_output = perforce.describe(changelist, show_shelved_files=True)[0]
    files = [value.decode() for key, value in describe_output.items() if b"depotFile" in key]

    # get workspace details
    client_details = perforce.get_client(perforce.client)[0]
    stream_root = client_details[b"Stream"].decode()

    files_not_under_root = [
        file_path for file_path in files if not file_path.lower().startswith(stream_root.lower())
    ]

    if files_not_under_root:
        files = ", ".join(files_not_under_root)
        raise ELIPYException(f"Files: {files} not in the expected stream: {stream_root}")

    LOGGER.info("Changelist is valid!")


def truncate_log_list(lines: list, message: str = "", line_limit: int = 100) -> None:
    """
    Log a line, cropping it if it exceeds the specified line limit.

    Parameters
    ----------
    lines: []
        List of lines to log.
    message : str, optional
        A message to log before the lines (default is an empty string).
    line_limit : int, optional
        The maximum number of lines allowed in the log (default is 100).

    Returns
    -------
    None
    """
    cropped_message = message
    if len(lines) > line_limit:
        cropped_message += "\n".join(map(str, lines[:line_limit]))
        cropped_message += f"\n[Message truncated: {len(lines) - line_limit} lines omitted]"
    else:
        cropped_message += "\n".join(map(str, lines))

    LOGGER.info(cropped_message)
