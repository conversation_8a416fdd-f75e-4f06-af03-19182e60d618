#*************************************************************
#  Sets up the initial needs to provide a vsphere instance
#*************************************************************
# Point to our provider
# https://www.terraform.io/docs/providers/vsphere/index.html
# *************************************************************
terraform {
  required_version = ">= 1.0.0"
  required_providers {
    vsphere = {
      version = "= 2.12.0" #locking Version to 2.12.0 to avoid introducing breaks
    }
    random = {
      source  = "hashicorp/random"
      version = "= 3.0.0"
    }
  }
}
provider "vsphere" {
  user           = var.vsphere_user
  password       = var.vsphere_password
  vsphere_server = var.vsphere_server
  # if you have a self-signed cert or not
  allow_unverified_ssl = true
  persist_session      = true
}

# *************************************************************
# Default Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "bct_network" {
  default     = "Buildfarm (VLAN 1028)"
  description = "Name of the network name to use in datacentre"
}
variable "bct_datacenter" {
  default     = "DICE"
  description = "The name of vcentre name to use e.g. DICE "
}
variable "bct_compute_cluster" {
  default     = "DICE-BUILD-PS"
  description = "Name of the compute cluster to use in datacenter"
}
variable "project_name" {
  default     = "bct_ps_preflight"
  description = "namer of the project e.g. diceupgradenext, walrus ect"
}
variable "jenkins_slave_labels" {
  default     = "super_statebuild"
  description = "Labels for jenkins slave"
}
variable "disk_size" {
  default     = "700"
  description = "size of the disk required in GB e.g. 700 = 700GB for C:"
}
variable "cpu_cores" {
  type        = string
  default     = "20"
  description = "cpu processes to be assigned to vm, ensure correct socket value is set also"
}
variable "cores_per_socket_value" {
  type        = string
  default     = "20"
  description = "cores per socket to assign, TRIPLE CHECK VALUE IS CORRECT!!"
}
variable "packer_template" {
  type        = string
  default     = "win10_22H2-cobra-v1.1101.0b76e848_PS"
  description = "Packer template for new vms. Existing vms will not be updated"
}
variable "hardware_version" {
  type        = number
  default     = 19
  description = "The hardware version for VMs in this project"
}
variable "local_admin_group" {
  type        = string
  default     = "AD\\BCT.SECURITY.BUILD.AGENTS"
  description = "Local admin group used for ansible and jenkins authentication"
}

# *************************************************************
# Environmental Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "vsphere_server" {}

variable "vsphere_user" {}

variable "vsphere_password" {}

variable "local_username" {}

variable "local_password" {}

variable "domain_password" {}

variable "domain_admin" {}

variable "project_dir" {}

variable "ARTIFACTORY_USER" {}

variable "ARTIFACTORY_PASSWORD" {}

variable "ARTIFACTORY_URL" {}

variable "commit_sha" {}

variable "commit_user" {}

variable "commit_url" {}
