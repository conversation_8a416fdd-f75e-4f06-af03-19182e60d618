"""
test_preflight_utils.py

Unit testing for preflight_utils
"""
import pytest
import unittest
from unittest.mock import MagicMock, patch
from dice_elipy_scripts.utils.preflight_utils import (
    raise_if_cl_not_exists,
    raise_if_wrong_stream,
    truncate_log_list,
)
from elipy2.exceptions import ELIPYException


class TestRaiseIfClNotExists(unittest.TestCase):
    def test_found_cl(self):
        perforce = MagicMock()
        raise_if_cl_not_exists(perforce, "1234")

    def test_not_found_cl(self):
        perforce = MagicMock()
        perforce.client = "random-client"
        perforce.get_client.return_value = [
            {
                b"Stream": b"//fbstream/dev-na",
            }
        ]
        perforce.describe.return_value = [
            {b"code": b"error", b"data": b"Invalid changelist number '1234'."}
        ]
        with pytest.raises(
            ELIPYException,
            match=r"Changelist does not exist: 1234",
        ):
            raise_if_cl_not_exists(perforce, "1234")

    def test_describe_timeout(self):
        perforce = MagicMock()
        perforce.client = "random-client"
        perforce.get_client.return_value = [
            {
                b"Stream": b"//fbstream/dev-na",
            }
        ]
        perforce.describe.return_value = [
            {
                b"code": b"error",
                b"data": b"TCP receive exceeded maximum configured duration of 120 seconds.",
            }
        ]
        with pytest.raises(
            ELIPYException,
            match=r".*Perforce timed out trying to run 'p4 describe'. Please retry your build.*",
        ):
            raise_if_cl_not_exists(perforce, "1234")


class TestRaiseIfWrongStream(unittest.TestCase):
    def test_right_stream(self):
        perforce = MagicMock()
        perforce.client = "random-client"
        perforce.get_client.return_value = [
            {
                b"Stream": b"//fbstream/dev-na",
            }
        ]
        perforce.describe.return_value = [
            {
                b"code": b"stat",
                b"depotFile0": b"//fbstream/dev-na/.p4ignore",
                b"depotFile1": b"//fbstream/dev-na/Frostbite.diceconfig",
            }
        ]
        raise_if_wrong_stream(perforce, "123")

    def test_right_stream_case_sensitive(self):
        perforce = MagicMock()
        perforce.client = "random-client"
        perforce.get_client.return_value = [
            {
                b"Stream": b"//bf/CH1/CH1-code-dev",
            }
        ]
        perforce.describe.return_value = [
            {
                b"code": b"stat",
                b"depotFile0": b"//bf/ch1/ch1-code-dev/TnT/Code/DICE/Extensions/DicePersistence/Kingston/Runtime/Private/Expressions/ClientPlayerStatReaderNode.ddf.cpp",
                b"depotFile1": b"//bf/ch1/ch1-code-dev/Frostbite.diceconfig",
            }
        ]
        raise_if_wrong_stream(perforce, "123")

    def test_wrong_stream(self):
        perforce = MagicMock()
        perforce.client = "random-client"
        perforce.get_client.return_value = [
            {
                b"Stream": b"//fbstream/dev-na",
            }
        ]
        perforce.describe.return_value = [
            {
                b"code": b"stat",
                b"depotFile0": b"//fbstream/dev-eu/.p4ignore",
                b"depotFile1": b"//fbstream/dev-eu/Frostbite.diceconfig",
            }
        ]
        with pytest.raises(
            ELIPYException,
            match=r"Files: //fbstream/dev-eu/.p4ignore, //fbstream/dev-eu/Frostbite.diceconfig not in the expected stream: .*",
        ):
            raise_if_wrong_stream(perforce, "123")


class TestTruncateLogList(unittest.TestCase):
    @patch("elipy2.LOGGER.info")
    def test_truncate_log_list_with_short_list(self, mock_info):
        lines = ["Line 1", "Line 2", "Line 3"]
        truncate_log_list(lines)
        mock_info.assert_called_once_with("Line 1\nLine 2\nLine 3")

    @patch("elipy2.LOGGER.info")
    def test_truncate_log_list_with_long_list(self, mock_info):
        lines = [f"Line {i}" for i in range(150)]
        truncate_log_list(lines, line_limit=100)
        expected_message = (
            "\n".join([f"Line {i}" for i in range(100)]) + "\n[Message truncated: 50 lines omitted]"
        )
        mock_info.assert_called_once_with(expected_message)

    @patch("elipy2.LOGGER.info")
    def test_truncate_log_list_with_message_prefix(self, mock_info):
        lines = ["Line 1", "Line 2", "Line 3"]
        truncate_log_list(lines, message="Prefix: ")
        mock_info.assert_called_once_with("Prefix: Line 1\nLine 2\nLine 3")

    @patch("elipy2.LOGGER.info")
    def test_truncate_log_list_with_long_list_and_message_prefix(self, mock_info):
        lines = [f"Line {i}" for i in range(120)]
        truncate_log_list(lines, message="Prefix: ", line_limit=100)
        expected_message = (
            "Prefix: "
            + "\n".join([f"Line {i}" for i in range(100)])
            + "\n[Message truncated: 20 lines omitted]"
        )
        mock_info.assert_called_once_with(expected_message)

    @patch("elipy2.LOGGER.info")
    def test_truncate_log_list_with_empty_list(self, mock_info):
        lines = []
        truncate_log_list(lines)
        mock_info.assert_called_once_with("")

    @patch("elipy2.LOGGER.info")
    def test_truncate_log_list_with_empty_list_and_message_prefix(self, mock_info):
        lines = []
        truncate_log_list(lines, message="Prefix: ")
        mock_info.assert_called_once_with("Prefix: ")
