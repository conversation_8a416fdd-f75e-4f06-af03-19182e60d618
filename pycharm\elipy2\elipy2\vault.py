"""
vault.py

Module that handles vault actions
"""

from __future__ import absolute_import
import os

from elipy2 import core, LOGGER, SETTINGS
from elipy2 import symbols, filer_paths
from elipy2.exceptions import ConfigValueNotFoundException, ELIPYException
from elipy2.frostbite import package_utils
from elipy2.telemetry import collect_metrics


@collect_metrics()
def vault(
    source,
    destination,
    platform,
    destination_path,
    change_list,
    product_name,
    code_branch,
    build_url=None,
    build_id=None,
    md5_validation=True,
    dry_run=False,
):
    """
    vaults $platform $source to $destination
    """
    LOGGER.info("Vault: Starting vault procedure from [{0}] to [{1}]".format(source, destination))
    vault_build(
        source=source,
        destination=destination,
        platform=platform,
        build_url=build_url,
        build_id=build_id,
        md5_validation=md5_validation,
        dry_run=dry_run,
    )
    vault_symbols(
        source=source,
        change_list=change_list,
        product_name=product_name,
        code_branch=code_branch,
        vault_destination=destination,
        build_url=build_url,
        build_id=build_id,
        platform=platform,
        symstore_vault_destination=destination_path,
        md5_validation=md5_validation,
        dry_run=dry_run,
    )


@collect_metrics()
def vault_build(
    source, destination, platform, build_url, build_id, md5_validation=True, dry_run=False
):  # pylint: disable=w0613
    """
    vaults build from $source to $destination
    """
    LOGGER.info("vault build: I vault build")
    validate_build_files(source, platform)
    LOGGER.info("Vault: Copying build from [{0}] to [{1}]".format(source, destination))
    if not dry_run:
        _vault_anything_with_checksum(source, destination, md5_validation=md5_validation)

    filename = "vault-build-" + build_id + ".log"
    if build_url is not None and not dry_run:
        _save_vaulting_logs(build_url=build_url, filename=filename, destination=destination)


def validate_build_files(source, platform):
    """
    check that required files are in source
    """
    try:
        required_files = SETTINGS.get("required_vault_files")[platform]
    except (ConfigValueNotFoundException, KeyError):
        LOGGER.warning("There are no required_vault_files set in yaml for {}".format(platform))
        return

    LOGGER.info("Checking for required files:\n  {}".format("\n  ".join(required_files)))
    exsisting_files = []
    for _, _, files in os.walk(source):
        exsisting_files += files
    for fil in required_files:
        if fil not in exsisting_files:
            raise ELIPYException("Required file, {}, missing from build".format(fil))
    LOGGER.info("All required files in build")


@collect_metrics()
def vault_symbols(
    source,
    build_url,
    build_id,
    platform,
    change_list,
    code_branch,
    product_name,
    vault_destination,
    symstore_vault_destination=None,
    md5_validation=True,
    dry_run=False,
    location=None,
):
    """
    This function does the following:
        1. Upload symbols to symstore if symstore vault location is provided (excluding linux)
            final config for server
            retail config for other platforms,
        2. Copies symbols to vault destination for all configs
    """
    if symstore_vault_destination is not None and "linux" not in platform.lower():
        symstore_config = "final" if "server" in platform.lower() else "retail"

        source_platform = platform
        if platform == "win64":
            source_platform = "win64game"

        if platform.lower() == "ps4":
            package_utils.ensure_package_is_installed("kettlesdk", fast_exists_check=True)
        elif platform.lower() == "ps5":
            package_utils.ensure_package_is_installed("ps5sdk", fast_exists_check=True)

        symstore_source = filer_paths.get_code_build_path(
            code_branch, change_list, source_platform, config=symstore_config, location=location
        )

        sym = symbols.SymbolsUtils()
        LOGGER.info(
            "Symbols: uploading symbols from [{0}] to [{1}]".format(
                symstore_source, symstore_vault_destination
            )
        )
        if not dry_run:
            try:
                sym.upload_symbols_to_sym_store(
                    platform=platform,
                    changelist=change_list,
                    product_name=product_name,
                    config=symstore_config,
                    sym_store=symstore_vault_destination,
                    path=symstore_source,
                )
            except Exception as exception:
                # Check if we should ignore PDB errors
                ignore_pdb_errors = (
                    SETTINGS.get("ignore_pdb_errors", default="false").lower() == "true"
                )
                if ignore_pdb_errors and (
                    ".pdb" in str(exception)
                    or "Failed to copy" in str(exception)
                    or "SYMSTORE ERROR" in str(exception)
                ):
                    LOGGER.warning(
                        "Symbol store upload failed but PDB errors are ignored: %s", str(exception)
                    )
                else:
                    raise

    vault_source = source or filer_paths.get_code_build_platform_path(
        code_branch, change_list, platform, location=location
    )

    LOGGER.info(
        "Symbols: Copying symbols from [{0}] to [{1}]".format(vault_source, vault_destination)
    )
    if not dry_run:
        _vault_anything_with_checksum(
            vault_source, vault_destination, md5_validation=md5_validation
        )

    filename = "vault-symbols-" + build_id + ".log"
    if build_url is not None and not dry_run:
        _save_vaulting_logs(build_url=build_url, filename=filename, destination=vault_destination)


def _vault_anything_with_checksum(source, destination, md5_validation):
    """
    does a full Vault copy including checksum generation
    """
    core.robocopy(source, destination, include_empty_dirs=False)
    if md5_validation:
        core.md5_check(source, destination)


def _save_vaulting_logs(build_url, filename, destination):
    cmd = [
        "C:\\ProgramData\\Chocolatey\\bin\\curl.exe",
        os.path.join(build_url, "consoleText"),
        "-o",
        os.path.join(destination, filename),
    ]
    core.run(cmd, print_std_out=True)


def save_symbols_locally(source, dest, clean=True):
    """
    fetches symbols from $source and puts them into $dest
    """
    LOGGER.info("Symbols: clean the folder before starting: [{0}]".format(clean))
    if clean:
        core.delete_folder(dest)
    LOGGER.info("Symbols: fetching symbols from [{0}]".format(source))
    core.robocopy(source, dest)
