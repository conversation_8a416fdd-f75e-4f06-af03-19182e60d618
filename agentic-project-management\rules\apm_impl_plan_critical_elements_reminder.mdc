---
description: Reminder for Implementation Plan: Ensure 1) Explicit Agent Assignment (consider distribution) and 2) 'Guiding Notes' for critical action steps are defined for ALL tasks.
globs: 
alwaysApply: false
---
# Implementation Plan: Critical Elements Checklist Reminder

When creating/reviewing `Implementation_Plan.md`, ensure for EVERY task:

1.  **Explicit Agent Assignment:** Is an agent clearly assigned? Have you considered distributing tasks to specialized agents? (Ref: Section 3.3 of guide)
2.  **"Guiding Notes" for Action Steps:** Are brief "Guidance:" notes included for critical action steps to ensure methodological consistency? (Ref: Section 3.4 of guide)

**Consult:** `prompts/01_Manager_Agent_Core_Guides/01_Implementation_Plan_Guide.md`.
