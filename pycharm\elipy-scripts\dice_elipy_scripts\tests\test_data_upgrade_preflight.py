"""
test_data_upgrade_preflight.py

Unit testing for data_upgrade_preflight
"""
import os
import tempfile
import unittest
from elipy2.exceptions import ELIPYException

from click.testing import Cli<PERSON>unner
from mock import call, MagicMock, patch
from dice_elipy_scripts.data_upgrade_preflight import (
    cli,
    create_build_directory,
    get_p4_files,
    process_upgrade_scripts_line,
    verify_upgrade_scripts,
)


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("elipy2.running_processes.kill", MagicMock())
@patch("dice_elipy_scripts.data_upgrade_preflight.add_sentry_tags", MagicMock())
class TestDataUpgrade(unittest.TestCase):
    OPTION_PENDING_CHANGELIST = "--pending-changelist"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_P4_USER = "--p4-user"
    OPTION_P4_STREAM = "--p4-stream"
    OPTION_DATA_DIRECTORY = "--data-directory"
    OPTION_P4_OUTPUT_FILE = "--p4-output-files"
    OPTION_DO_BUILD_FROSTED = "--build-frosted"

    VALUE_PENDING_CHANGELIST = "1234"
    VALUE_P4_PORT = "p4_port"
    VALUE_P4_CLIENT = "p4_client"
    VALUE_P4_USER = "p4_user"
    VALUE_P4_STREAM = "//bf/mainline/trunk-code-dev"
    VALUE_DATA_DIRECTORY = "data_directory"
    VALUE_P4_OUTPUT_FILE = "p4sync.txt"
    VALUE_P4_OUTPUT_FILE2 = "p4unshelve.txt"

    BASIC_ARGS = [
        OPTION_PENDING_CHANGELIST,
        VALUE_PENDING_CHANGELIST,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
        OPTION_P4_USER,
        VALUE_P4_USER,
    ]

    def setUp(self):
        self.patcher_os_chdir = patch("os.chdir")
        self.mock_os_chdir = self.patcher_os_chdir.start()

        self.patcher_set_datadir = patch("elipy2.data.DataUtils.set_datadir")
        self.mock_set_datadir = self.patcher_set_datadir.start()

        self.patcher_p4utils = patch("elipy2.p4.P4Utils")
        self.mock_p4utils = self.patcher_p4utils.start()
        self.mock_p4utils.return_value.unshelve.return_value = [
            {
                b"code": b"stat",
                b"depotFile": b"//bf/mainline/trunk-code-dev/TnT/Code/file1.txt",
                b"rev": b"1",
                b"action": b"edit",
            },
            {
                b"code": b"stat",
                b"depotFile": b"//bf/mainline/trunk-code-dev/bfdata/data/file2.dbx",
                b"rev": b"1",
                b"action": b"edit",
            },
        ]

        self.patcher_ensure_p4_config = patch("elipy2.core.ensure_p4_config")
        self.mock_ensure_p4_config = self.patcher_ensure_p4_config.start()

        self.patcher_core_run = patch("elipy2.core.run")
        self.mock_core_run = self.patcher_core_run.start()

        self.patcher_get_game_data_dir = patch("elipy2.frostbite_core.get_game_data_dir")
        self.mock_get_game_data_dir = self.patcher_get_game_data_dir.start()
        self.mock_get_game_data_dir.return_value = "game_data_dir"

        self.patcher_logger_info = patch("elipy2.LOGGER.info")
        self.mock_logger_info = self.patcher_logger_info.start()

        self.patcher_codeutils = patch("elipy2.code.CodeUtils", autospec=True)
        self.mock_codeutils = self.patcher_codeutils.start()

    def tearDown(self):
        patch.stopall()

    @patch("dice_elipy_scripts.data_upgrade_preflight.verify_upgrade_scripts")
    def test_basic_run(self, verify_upgrade_scripts):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        verify_upgrade_scripts.return_value = []
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.unshelve.call_count == 1
        assert self.mock_core_run.call_count == 1
        assert self.mock_codeutils.return_value.buildsln.call_count == 0

    @patch("dice_elipy_scripts.data_upgrade_preflight.get_p4_files")
    @patch("dice_elipy_scripts.data_upgrade_preflight.verify_upgrade_scripts")
    def test_run_p4_output_file(self, verify_upgrade_scripts, get_p4_files):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_P4_PORT,
                self.VALUE_P4_PORT,
                self.OPTION_P4_CLIENT,
                self.VALUE_P4_CLIENT,
                self.OPTION_P4_USER,
                self.VALUE_P4_USER,
                self.OPTION_P4_OUTPUT_FILE,
                self.VALUE_P4_OUTPUT_FILE,
                self.OPTION_P4_STREAM,
                self.VALUE_P4_STREAM,
            ],
        )
        verify_upgrade_scripts.return_value = []
        get_p4_files.return_value = []
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.unshelve.call_count == 0
        assert self.mock_core_run.call_count == 1
        assert self.mock_codeutils.return_value.buildsln.call_count == 0

    @patch("dice_elipy_scripts.data_upgrade_preflight.get_p4_files")
    @patch("dice_elipy_scripts.data_upgrade_preflight.verify_upgrade_scripts")
    def test_run_two_p4_output_files(self, verify_upgrade_scripts, get_p4_files):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_P4_PORT,
                self.VALUE_P4_PORT,
                self.OPTION_P4_CLIENT,
                self.VALUE_P4_CLIENT,
                self.OPTION_P4_USER,
                self.VALUE_P4_USER,
                self.OPTION_P4_OUTPUT_FILE,
                self.VALUE_P4_OUTPUT_FILE,
                self.OPTION_P4_OUTPUT_FILE,
                self.VALUE_P4_OUTPUT_FILE2,
                self.OPTION_P4_STREAM,
                self.VALUE_P4_STREAM,
            ],
        )
        verify_upgrade_scripts.return_value = []
        get_p4_files.return_value = []
        assert verify_upgrade_scripts.call_count == 1
        assert get_p4_files.call_count == 2
        assert get_p4_files.mock_calls[0] == call(self.VALUE_P4_OUTPUT_FILE, self.VALUE_P4_STREAM)
        assert get_p4_files.mock_calls[2] == call(self.VALUE_P4_OUTPUT_FILE2, self.VALUE_P4_STREAM)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.unshelve.call_count == 0
        assert self.mock_core_run.call_count == 1

    @patch("dice_elipy_scripts.data_upgrade_preflight.verify_upgrade_scripts")
    def test_run_two_p4_output_files_validate_verify(self, verify_upgrade_scripts):
        with tempfile.TemporaryDirectory() as temp_dir:
            with open(os.path.join(temp_dir, self.VALUE_P4_OUTPUT_FILE), "w") as fp:
                fp.write(
                    "[pitee] Loading settings from: C:\\_tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\n"
                )
                fp.write(
                    "[pitee] Running command: pitee.exe 'C:\\tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\\p4.exe -p p4port:2001 -u DICE\\svc_act -c partitioned_ws_BCT-Staging_CH1-code-dev_s2c-cwgf-ss-6vru_ac9dbdf4 sync //partitioned_ws_BCT-Staging_CH1-code-dev_s2c-cwgf-ss-6vru_ac9dbdf4/...@22754874\n"
                )
                fp.write(
                    "//bf/mainline/trunk-code-dev/TnT/file1.cpp#4 - updating F:\\dev\\tnt\\file1.cpp\n"
                )
                fp.write(
                    "//bf/mainline/trunk-code-dev/TnT/Code/file2.cpp#6 - updating F:\\dev\\TnT\\Code\\file2.cpp\n"
                )
                fp.close()
            with open(os.path.join(temp_dir, self.VALUE_P4_OUTPUT_FILE2), "w") as fp:
                fp.write(
                    "[pitee] Loading settings from: C:\\_tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\n"
                )
                fp.write(
                    "[pitee] Running command: pitee.exe 'C:\\_tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\\p4.exe -p p4port:2001 -u DICE\\svc_act -c partitioned_ws_Glacier_CH1-content-dev_c1-dw3-ss-kzgl_c4b5d253 unshelve -s 22787633'\n"
                )
                fp.write(
                    "//bf/mainline/trunk-code-dev/bfdata/Source/Game/GlacierMP/Levels/MP_Dumbo/_Layers_Gameplay/Rush0.dbx#11 - unshelved, opened for edit\n"
                )
                fp.write(
                    "//bf/mainline/trunk-code-dev/bfdata/Source/Game/GlacierMP/Levels/MP_Dumbo/_Layers_Gameplay/Rush0_Schematic.dbx#4 - unshelved, opened for edit\n"
                )
                fp.close()
            runner = CliRunner()
            result = runner.invoke(
                cli,
                [
                    self.OPTION_P4_PORT,
                    self.VALUE_P4_PORT,
                    self.OPTION_P4_CLIENT,
                    self.VALUE_P4_CLIENT,
                    self.OPTION_P4_USER,
                    self.VALUE_P4_USER,
                    self.OPTION_P4_OUTPUT_FILE,
                    os.path.join(temp_dir, self.VALUE_P4_OUTPUT_FILE),
                    self.OPTION_P4_OUTPUT_FILE,
                    os.path.join(temp_dir, self.VALUE_P4_OUTPUT_FILE2),
                    self.OPTION_P4_STREAM,
                    self.VALUE_P4_STREAM,
                ],
            )
            verify_upgrade_scripts.return_value = []
            assert verify_upgrade_scripts.call_count == 1
            assert sorted(verify_upgrade_scripts.mock_calls[0][1][0]) == sorted(
                [
                    "//bf/mainline/trunk-code-dev/bfdata/Source/Game/GlacierMP/Levels/MP_Dumbo/_Layers_Gameplay/Rush0.dbx",
                    "//bf/mainline/trunk-code-dev/TnT/Code/file2.cpp",
                    "//bf/mainline/trunk-code-dev/TnT/file1.cpp",
                    "//bf/mainline/trunk-code-dev/bfdata/Source/Game/GlacierMP/Levels/MP_Dumbo/_Layers_Gameplay/Rush0_Schematic.dbx",
                ]
            )
            assert "Usage:" not in result.output
            assert result.exit_code == 0
            assert self.mock_p4utils.return_value.unshelve.call_count == 0
            assert self.mock_core_run.call_count == 1

    @patch("dice_elipy_scripts.data_upgrade_preflight.verify_upgrade_scripts")
    def test_run_p4_output_file_and_pending_cl(self, verify_upgrade_scripts):
        with tempfile.TemporaryDirectory() as temp_dir:
            with open(os.path.join(temp_dir, self.VALUE_P4_OUTPUT_FILE), "w") as fp:
                fp.write(
                    "[pitee] Loading settings from: C:\\_tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\n"
                )
                fp.write(
                    "[pitee] Running command: pitee.exe 'C:\\tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\\p4.exe -p p4port:2001 -u DICE\\svc_act -c partitioned_ws_BCT-Staging_CH1-code-dev_s2c-cwgf-ss-6vru_ac9dbdf4 sync //partitioned_ws_BCT-Staging_CH1-code-dev_s2c-cwgf-ss-6vru_ac9dbdf4/...@22754874\n"
                )
                fp.write(
                    "//bf/mainline/trunk-code-dev/TnT/file1.cpp#4 - updating F:\\dev\\tnt\\file1.cpp\n"
                )
                fp.write(
                    "//bf/mainline/trunk-code-dev/TnT/Code/file2.cpp#6 - updating F:\\dev\\TnT\\Code\\file2.cpp\n"
                )
                fp.close()
            runner = CliRunner()
            result = runner.invoke(
                cli,
                [
                    self.OPTION_P4_PORT,
                    self.VALUE_P4_PORT,
                    self.OPTION_P4_CLIENT,
                    self.VALUE_P4_CLIENT,
                    self.OPTION_P4_USER,
                    self.VALUE_P4_USER,
                    self.OPTION_P4_OUTPUT_FILE,
                    os.path.join(temp_dir, self.VALUE_P4_OUTPUT_FILE),
                    self.OPTION_P4_STREAM,
                    self.VALUE_P4_STREAM,
                    self.OPTION_PENDING_CHANGELIST,
                    self.VALUE_PENDING_CHANGELIST,
                ],
            )
            verify_upgrade_scripts.return_value = []
            assert verify_upgrade_scripts.call_count == 1
            assert sorted(verify_upgrade_scripts.mock_calls[0][1][0]) == sorted(
                [
                    "//bf/mainline/trunk-code-dev/TnT/Code/file1.txt",
                    "//bf/mainline/trunk-code-dev/TnT/Code/file2.cpp",
                    "//bf/mainline/trunk-code-dev/TnT/file1.cpp",
                    "//bf/mainline/trunk-code-dev/bfdata/data/file2.dbx",
                ]
            )
            assert "Usage:" not in result.output
            assert result.exit_code == 0
            assert self.mock_p4utils.return_value.unshelve.call_count == 1
            assert self.mock_core_run.call_count == 1

    def test_args_fail_none_required(self):
        fail_args = [
            self.OPTION_P4_PORT,
            self.VALUE_P4_PORT,
            self.OPTION_P4_CLIENT,
            self.VALUE_P4_CLIENT,
            self.OPTION_P4_USER,
            self.VALUE_P4_USER,
        ]
        runner = CliRunner()
        result = runner.invoke(cli, fail_args)
        verify_upgrade_scripts.return_value = []
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        assert (
            f"{result.exception}" == "Either --p4-output-files or --pending-changelist must be set."
        )
        assert self.mock_p4utils.return_value.unshelve.call_count == 0
        assert self.mock_core_run.call_count == 0

    def test_args_fail_p4_no_stream(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_P4_PORT,
                self.VALUE_P4_PORT,
                self.OPTION_P4_CLIENT,
                self.VALUE_P4_CLIENT,
                self.OPTION_P4_USER,
                self.VALUE_P4_USER,
                self.OPTION_P4_OUTPUT_FILE,
                self.VALUE_P4_OUTPUT_FILE,
            ],
        )
        verify_upgrade_scripts.return_value = []
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        assert f"{result.exception}" == "--p4-stream is required with --p4-output-files."
        assert self.mock_p4utils.return_value.unshelve.call_count == 0
        assert self.mock_core_run.call_count == 0

    @patch("dice_elipy_scripts.data_upgrade_preflight.verify_upgrade_scripts")
    def test_not_running_upgrade_scripts_no_frosted(self, verify_upgrade_scripts):
        verify_upgrade_scripts.return_value = []
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_DATA_DIRECTORY,
                self.VALUE_DATA_DIRECTORY,
                self.OPTION_DO_BUILD_FROSTED,
                "true",
            ],
        )
        self.mock_logger_info.assert_has_calls(
            [
                call("Not necessary to run upgrade scripts. No elegible files detected"),
            ],
            any_order=True,
        )
        assert self.mock_p4utils.return_value.unshelve.call_count == 1
        assert self.mock_codeutils.return_value.buildsln.call_count == 0
        assert result.exit_code == 0
        self.mock_set_datadir.assert_called_once_with(self.VALUE_DATA_DIRECTORY)

    @patch("dice_elipy_scripts.data_upgrade_preflight.verify_upgrade_scripts")
    def test_running_upgrade_scripts_yes_frosted(self, verify_upgrade_scripts):
        verify_upgrade_scripts.return_value = ["file1.txt", "file2.dbx"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_DATA_DIRECTORY,
                self.VALUE_DATA_DIRECTORY,
                self.OPTION_DO_BUILD_FROSTED,
                "true",
            ],
        )
        self.mock_logger_info.assert_has_calls(
            [
                call("Running upgrade scripts. Changes found in files: ['file1.txt', 'file2.dbx']"),
            ],
            any_order=True,
        )
        assert self.mock_p4utils.return_value.unshelve.call_count == 1
        assert self.mock_core_run.call_count == 1
        assert result.exit_code == 0
        assert self.mock_codeutils.return_value.buildsln.call_count == 1
        self.mock_set_datadir.assert_called_once_with(self.VALUE_DATA_DIRECTORY)

    @patch("dice_elipy_scripts.data_upgrade_preflight.verify_upgrade_scripts")
    def test_running_upgrade_scripts(self, verify_upgrade_scripts):
        verify_upgrade_scripts.return_value = ["file1.txt", "file2.dbx"]
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_DATA_DIRECTORY, self.VALUE_DATA_DIRECTORY]
        )
        self.mock_logger_info.assert_has_calls(
            [
                call("Running upgrade scripts. Changes found in files: ['file1.txt', 'file2.dbx']"),
            ],
            any_order=True,
        )
        assert self.mock_p4utils.return_value.unshelve.call_count == 1
        assert self.mock_core_run.call_count == 1
        assert result.exit_code == 0
        assert self.mock_codeutils.return_value.buildsln.call_count == 0
        self.mock_set_datadir.assert_called_once_with(self.VALUE_DATA_DIRECTORY)

    @patch("os.makedirs")
    @patch("os.path.exists")
    def test_create_build_directory(self, mock_os_path_exists, mock_os_makedirs):
        mock_os_path_exists.return_value = False
        create_build_directory()
        mock_os_makedirs.assert_called_once()

    def test_comment_upgrade_scripts_line(self):
        result = process_upgrade_scripts_line("# dev\\path", "dev")
        self.assertEqual(result, (None, None))

    def test_excluded_upgrade_scripts_line(self):
        result = process_upgrade_scripts_line("- base\\dev\\path", "dev")
        assert result[0] is None
        assert result[1].endswith("path")
        assert result[1].startswith("dev")

    def test_included_upgrade_scripts_line(self):
        result = process_upgrade_scripts_line("base\\dev\\path", "dev")
        assert result[1] == None
        assert result[0].startswith("dev")
        assert result[0].endswith("path")

    @patch("dice_elipy_scripts.data_upgrade_preflight.read_upgrade_scripts_file")
    def test_valid_verify_upgrade_scripts(self, read_upgrade_scripts_file):
        NOT_A_PATH = os.path.join("#not", "a", "path")
        DUMMY_FILE_PATH = os.path.join("-TnT", "dummy.txt")
        UPGRADE_SCRIPT_PATH = os.path.join(
            "TnT",
            "Code",
            "DICE",
            "BattlefieldGame",
            "Scripts",
            "UpgradeScripts",
            "ActiveUpgradeScripts",
        )

        read_upgrade_scripts_file.return_value = [NOT_A_PATH, DUMMY_FILE_PATH, UPGRADE_SCRIPT_PATH]

        filelist = [
            "//bf/mainline/trunk-code-dev/TnT/Code/DICE/BattlefieldGame/Scripts/UpgradeScripts/ActiveUpgradeScripts/Dummy.txt",
            "//bf/mainline/trunk-code-dev/TnT/dummy.txt",
        ]

        result = verify_upgrade_scripts(filelist, "TnT")

        # Assert that the function returned the correct result
        self.assertEqual(
            result,
            [
                os.path.normpath(
                    "TnT/Code/DICE/BattlefieldGame/Scripts/UpgradeScripts/ActiveUpgradeScripts/Dummy.txt"
                )
            ],
        )

    @patch("dice_elipy_scripts.data_upgrade_preflight.read_upgrade_scripts_file")
    def test_excluded_verify_upgrade_scripts(self, read_upgrade_scripts_file):
        read_upgrade_scripts_file.return_value = [
            "-TnT\\dummy.txt",
        ]
        filelist = ["//bf/mainline/trunk-code-dev/TnT/dummy.txt"]

        result = verify_upgrade_scripts(filelist, "TnT")

        # Assert that the function returned the correct result
        self.assertEqual(result, [])

        self.mock_logger_info.assert_has_calls(
            [
                call(
                    f"File {os.path.normpath('TnT/dummy.txt')} was excluded based on UpgradeScripts.txt"
                ),
            ],
            any_order=True,
        )

    def test_p4_output_file_not_exist(self):
        p4_files = get_p4_files("nonexistent.txt", "stream")
        self.mock_logger_info.assert_has_calls(
            [
                call("File nonexistent.txt does not exist."),
            ],
            any_order=True,
        )
        assert p4_files == []

    def test_get_p4_files_sync(self):
        with tempfile.NamedTemporaryFile(delete=False) as fp:
            fp.write(
                b"[pitee] Loading settings from: C:\\_tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\n"
            )
            fp.write(
                b"[pitee] Running command: pitee.exe 'C:\\tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\\p4.exe -p p4port:2001 -u DICE\\svc_act -c partitioned_ws_BCT-Staging_CH1-code-dev_s2c-cwgf-ss-6vru_ac9dbdf4 sync //partitioned_ws_BCT-Staging_CH1-code-dev_s2c-cwgf-ss-6vru_ac9dbdf4/...@22754874\n"
            )
            fp.write(
                b"//bf/mainline/trunk-code-dev/TnT/file1.cpp#4 - updating F:\\dev\\tnt\\file1.cpp\n"
            )
            fp.write(
                b"//bf/mainline/trunk-code-dev/TnT/Code/file2.cpp#6 - updating F:\\dev\\TnT\\Code\\file2.cpp\n"
            )
            fp.close()
            result = get_p4_files(fp.name, "//bf/mainline/trunk-code-dev")
        self.assertEqual(
            result,
            [
                "//bf/mainline/trunk-code-dev/TnT/file1.cpp",
                "//bf/mainline/trunk-code-dev/TnT/Code/file2.cpp",
            ],
        )

    def test_get_p4_files_unshelve(self):
        with tempfile.NamedTemporaryFile(delete=False) as fp:
            fp.write(
                b"[pitee] Loading settings from: C:\\_tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\n"
            )
            fp.write(
                b"[pitee] Running command: pitee.exe 'C:\\_tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\\p4.exe -p p4port:2001 -u DICE\\svc_act -c partitioned_ws_Glacier_CH1-content-dev_c1-dw3-ss-kzgl_c4b5d253 unshelve -s 22787633'\n"
            )
            fp.write(
                b"//bf/CH1/CH1-content-dev/bfdata/Source/Game/GlacierMP/Levels/MP_Dumbo/_Layers_Gameplay/Rush0.dbx#11 - unshelved, opened for edit\n"
            )
            fp.write(
                b"//bf/CH1/CH1-content-dev/bfdata/Source/Game/GlacierMP/Levels/MP_Dumbo/_Layers_Gameplay/Rush0_Schematic.dbx#4 - unshelved, opened for edit\n"
            )
            fp.close()
            result = get_p4_files(fp.name, "//bf/CH1/CH1-content-dev")
        self.assertEqual(
            result,
            [
                "//bf/CH1/CH1-content-dev/bfdata/Source/Game/GlacierMP/Levels/MP_Dumbo/_Layers_Gameplay/Rush0.dbx",
                "//bf/CH1/CH1-content-dev/bfdata/Source/Game/GlacierMP/Levels/MP_Dumbo/_Layers_Gameplay/Rush0_Schematic.dbx",
            ],
        )
