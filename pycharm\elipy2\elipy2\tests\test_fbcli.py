"""
test_fbcli.py
"""
import pytest
from mock import MagicMock, patch
from elipy2 import SETTINGS
from elipy2.exceptions import ELIPYException
from elipy2.frostbite import fbcli


@patch("elipy2.telemetry.upload_metrics", MagicMock())
class TestFbcli:
    @patch("elipy2.core.run")
    def test_run(self, mock_core_run):
        cmd = "icepick"
        args = ["ps4", "animation", "--config", "release"]
        expected_cmd = fbcli.FBCLI_CMD_PATH + ["icepick"] + args
        expected_env_patch = {"EXECUTOR": "fbcli.py"}
        fbcli.run(cmd, method_args=args)
        mock_core_run.assert_called_once_with(
            expected_cmd,
            allow_non_zero_exit_code=False,
            print_std_out=True,
            capture_std_out=True,
            write_std_out=False,
            env_patch=expected_env_patch,
        )
        actual_cmd = " ".join(mock_core_run.call_args[0][0])
        assert actual_cmd == "{0} icepick ps4 animation --config release".format(
            " ".join(fbcli.FBCLI_CMD_PATH)
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_gensln(self, mock_fbcli_run):
        fbcli.gensln(target="ps4", alltests=True, nomaster=True, variants=["release"])
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="gensln",
            method_args=["ps4", "release", "-alltests", "-nomaster"],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_gensln_wsl(self, mock_fbcli_run):
        fbcli.gensln(
            target="linux64", alltests=False, nomaster=False, wsl=True, variants=["release"]
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="gensln",
            method_args=["linux64", "release", "-wsl"],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_gensln_framework_args(self, mock_fbcli_run):
        fbcli.gensln(
            target="ps4",
            alltests=True,
            nomaster=True,
            variants=["release"],
            framework_args=["some-args"],
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="gensln",
            method_args=["ps4", "release", "-alltests", "-nomaster", "--", "some-args"],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_buildsln(self, mock_fbcli_run):
        fbcli.buildsln(target="ps4", config="release", msbuild_args=[], fail_on_first_error=True)
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="buildsln",
            method_args=["ps4", "release", "-fail_on_first_error"],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_buildsln_msbuild_args(self, mock_fbcli_run):
        fbcli.buildsln(
            target="xb1", config="release", msbuild_args=["/m:4"], fail_on_first_error=True
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="buildsln",
            method_args=["xb1", "release", "-fail_on_first_error", "--", "/m:4"],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_nant(self, mock_fbcli_run):
        fbcli.nant(
            package="EAIO",
            platform="pc64-vc-dll-release",
            target="gensln",
            vsver="2022",
            enableexpiredapierror=True,
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="nant",
            method_args=[
                "EAIO",
                "pc64-vc-dll-release",
                "gensln",
                "-vsver",
                "2022",
                "-enableexpiredapierror",
            ],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_cook(self, mock_fbcli_run):
        fbcli.cook(
            platforms=["ps4"],
            config="release",
            assets=["game"],
            pipeline_args=[],
            attach=False,
            is_local=False,
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="cook",
            method_args=["ps4", "game", "-release"],
            print_std_out=True,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_cook_multiple_assets(self, mock_fbcli_run):
        fbcli.cook(
            platforms=["ps4"],
            config="release",
            assets=["game", "foo"],
            pipeline_args=[],
            attach=False,
            is_local=False,
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="cook",
            method_args=["ps4", "game foo", "-release"],
            print_std_out=True,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_icepick_cook(self, mock_fbcli_run):
        fbcli.icepick_cook(
            platform="ps4",
            configuration="release",
            suites=["game"],
            args=["pipeline-args"],
            is_local=False,
            extra_framework_args=["framework-arg"],
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="icepickcook",
            method_args=[
                "ps4",
                "game",
                "-release",
                "--extra-framework-args=framework-arg",
                "--",
                "pipeline-args",
            ],
            print_std_out=True,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_icepick_cook_multiple_assets(self, mock_fbcli_run):
        fbcli.icepick_cook(
            platform="ps4",
            configuration="release",
            suites=["game", "rendering"],
            args=["pipeline-args"],
            is_local=False,
            extra_framework_args=["framework-arg"],
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="icepickcook",
            method_args=[
                "ps4",
                "game",
                "rendering",
                "-release",
                "--extra-framework-args=framework-arg",
                "--",
                "pipeline-args",
            ],
            print_std_out=True,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_pushbuild(self, mock_fbcli_run):
        label = "13115473"
        artifact = "ExampleGame"
        platforms = ["ps4"]
        expected_buildshare = SETTINGS.get("build_share", SETTINGS.location)
        expected_default_config = "release"
        expected_args = (
            ["-path", expected_buildshare, label, artifact] + platforms + [expected_default_config]
        )
        fbcli.pushbuild(label, artifact, platforms)
        mock_fbcli_run.assert_called_once_with("pushbuild", expected_args)

    @patch("elipy2.frostbite.fbcli.run")
    def test_pushbuild_path_subpath_multiple_platforms(self, mock_fbcli_run):
        label = "13115473"
        artifact = "FBNullLicensee"
        platforms = ["win64", "pipeline"]
        subpath = "FBNullLicensee"
        path = "\\mock\\path"
        config = "retail"
        expected_args = ["-path", path, label, artifact] + platforms + [config, "-subpath", subpath]
        fbcli.pushbuild(label, artifact, platforms, config=config, path=path, subpath=subpath)
        mock_fbcli_run.assert_called_once_with("pushbuild", expected_args)

    @patch("elipy2.frostbite.fbcli.run")
    def test_pushbuild_invalid_args(self, mock_fbcli_run):
        label = "13115473"
        artifact = "ExampleGame"
        platforms = ["ps5"]
        with pytest.raises(ValueError):
            fbcli.pushbuild(None, artifact, platforms)
            mock_fbcli_run.assert_not_called()
        with pytest.raises(ValueError):
            fbcli.pushbuild(label, None, platforms)
            mock_fbcli_run.assert_not_called()
        with pytest.raises(ValueError):
            fbcli.pushbuild(label, artifact, platforms=[])
            mock_fbcli_run.assert_not_called()
        with pytest.raises(TypeError):
            fbcli.pushbuild(label, artifact, platforms="ps5")
            mock_fbcli_run.assert_not_called()

    @patch("elipy2.frostbite.fbcli.run")
    def test_pushbuild_eacopy_args(self, mock_fbcli_run):
        label = "13115473"
        artifact = "ExampleGame"
        platforms = ["ps4"]
        eacopy_args = ["my", "args"]
        expected_buildshare = SETTINGS.get("build_share", SETTINGS.location)
        expected_default_config = "release"
        expected_args = (
            ["-path", expected_buildshare, label, artifact]
            + platforms
            + [expected_default_config]
            + ["--"]
            + eacopy_args
        )
        fbcli.pushbuild(label, artifact, platforms, eacopy_args=eacopy_args)
        mock_fbcli_run.assert_called_once_with("pushbuild", expected_args)

    @patch("elipy2.frostbite.fbcli.run")
    def test_pullbuild(self, mock_fbcli_run):
        label = "13115473"
        artifact = "ExampleGame"
        platforms = ["ps4"]
        expected_buildshare = SETTINGS.get("build_share", SETTINGS.location)
        expected_default_config = "release"
        expected_args = (
            ["-ignorelock", "-local", "-path", expected_buildshare, label, artifact]
            + platforms
            + [expected_default_config]
        )
        fbcli.pullbuild(label, artifact, platforms)
        mock_fbcli_run.assert_called_once_with("pullbuild", expected_args)

    @patch("elipy2.frostbite.fbcli.run")
    def test_pullbuild_false_default_args(self, mock_fbcli_run):
        label = "13115473"
        artifact = "ExampleGame"
        platforms = ["pipeline", "win64", "frosted"]
        local = (False,)
        ignore_lock = (False,)
        expected_buildshare = SETTINGS.get("build_share", SETTINGS.location)
        expected_default_config = "release"
        expected_args = (
            ["-path", expected_buildshare, label, artifact] + platforms + [expected_default_config]
        )
        fbcli.pullbuild(label, artifact, platforms, local=local, ignore_lock=ignore_lock)
        mock_fbcli_run.assert_called_once_with("pullbuild", expected_args)

    @patch("elipy2.frostbite.fbcli.run")
    def test_pullbuild_path_subpath(self, mock_fbcli_run):
        label = "13115473"
        artifact = "ExampleGame"
        platforms = ["pipeline", "win64", "frosted"]
        path = "\\mock\\share"
        config = "retail"
        subpath = "fbnext"
        expected_args = (
            ["-ignorelock", "-local", "-path", path, label, artifact]
            + platforms
            + [config, "-subpath", subpath]
        )
        fbcli.pullbuild(label, artifact, platforms, config=config, path=path, subpath=subpath)
        mock_fbcli_run.assert_called_once_with("pullbuild", expected_args)

    @patch("elipy2.core.run")
    def test_run_no_method_args(self, mock_core_run):
        cmd = "icepick"
        expected_cmd = fbcli.FBCLI_CMD_PATH + [cmd]
        expected_env_patch = {"EXECUTOR": "fbcli.py"}
        fbcli.run(cmd)
        mock_core_run.assert_called_once_with(
            expected_cmd,
            allow_non_zero_exit_code=False,
            print_std_out=True,
            capture_std_out=True,
            write_std_out=False,
            env_patch=expected_env_patch,
        )
        actual_cmd = " ".join(mock_core_run.call_args[0][0])
        assert actual_cmd == "{0} icepick".format(" ".join(fbcli.FBCLI_CMD_PATH))

    @patch("elipy2.frostbite.fbcli.run")
    def test_enable_licensee(self, mock_fbcli_run):
        fbcli.set_licensee("enable", "some-licensee")

        mock_fbcli_run.assert_called_once_with(
            fbcli_method="licensee",
            method_args=["enable", "some-licensee"],
            print_std_out=True,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_disable_licensee(self, mock_fbcli_run):
        fbcli.set_licensee("disable", "some-licensee")

        mock_fbcli_run.assert_called_once_with(
            fbcli_method="licensee",
            method_args=["disable", "some-licensee"],
            print_std_out=True,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_switch_licensee(self, mock_fbcli_run):
        fbcli.set_licensee("switch", "some-licensee")

        mock_fbcli_run.assert_called_once_with(
            fbcli_method="licensee",
            method_args=["switch", "some-licensee"],
            print_std_out=True,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_view_licensee(self, mock_fbcli_run):
        fbcli.set_licensee("view")

        mock_fbcli_run.assert_called_once_with(
            fbcli_method="licensee",
            method_args=["view"],
            print_std_out=True,
        )

    def test_set_licensee_undefined_action(self):
        with pytest.raises(ELIPYException):
            fbcli.set_licensee("undefined_action")

    def test_run_no_method(self):
        args = ["ps4", "animation", "--config", "release"]
        with pytest.raises(TypeError):
            fbcli.run(method_args=args)
        with pytest.raises(ValueError):
            fbcli.run(None, method_args=args)

    def test_append_fbcli_arg_condition_True(self):
        arg_list = ["test"]
        fbcli.append_fbcli_arg_condition(arg_list, "arg1", 3, True)
        fbcli.append_fbcli_arg_condition(arg_list, "arg2", "Break!", True)
        assert arg_list == ["test", "arg1", 3, "arg2", "Break!"]

    def test_append_fbcli_arg_condition_False(self):
        arg_list = ["test"]
        fbcli.append_fbcli_arg_condition(arg_list, "arg1", 3, True)
        fbcli.append_fbcli_arg_condition(arg_list, "arg2", "Break!", False)
        assert arg_list == ["test", "arg1", 3]

    def test_append_fbcli_arg_condition_None_Value(self):
        arg_list = []
        fbcli.append_fbcli_arg_condition(arg_list, "--arg1", None, True)
        assert arg_list == ["--arg1"]

    @patch("elipy2.frostbite.fbcli.run")
    def test_install_prerequisites_default(self, mock_fbcli_run):
        fbcli.install_prerequisites()

        mock_fbcli_run.assert_called_once_with(
            fbcli_method="install",
            method_args=["prerequisites", "-buildmachine", "-noavalanche"],
            print_std_out=True,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_install_prerequisites_custom(self, mock_fbcli_run):
        fbcli.install_prerequisites(buildmachine=False, noavalanche=False)

        mock_fbcli_run.assert_called_once_with(
            fbcli_method="install",
            method_args=["prerequisites"],
            print_std_out=True,
        )
