#*************************************************************
#  Sets up the initial needs to provide a vspehere instance
#*************************************************************
# Point to our provider
# https://www.terraform.io/docs/providers/vsphere/index.html
# *************************************************************
terraform {
  required_version = ">= 1.0.0"
  required_providers {
    vsphere = {
      version = "= 2.12.0" #locking Version to 2.12.0 to avoid introducing breaks
    }
    random = {
      source  = "hashicorp/random"
      version = "= 3.0.0"
    }
  }
}

provider "vsphere" {
  user           = var.vsphere_user
  password       = var.vsphere_password
  vsphere_server = var.vsphere_server
  # if you have a self-signed cert or not
  allow_unverified_ssl = true
  persist_session      = true
}
# *************************************************************
# Default Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "project_name" {
  default     = "snowcache_troy_criterion"
  description = "namer of the project e.g. casablanca, walrus ect"
}

variable "packer_template" {
  default     = "win10_22H2-cobra-v1.1101.0b76e848_PS"
  description = "The name of the template on the datacenter to use"
}
variable "network" {
  default     = "Buildfarm (VLAN 1028)"
  description = "Name of the network name to use in datacentre"
}
variable "datacenter" {
  default     = "DICE"
  description = "The name of vcentre name to use e.g. DICE "
}
variable "vsphere_compute_cluster" {
  default     = "DRE UK Build Hardware"
  description = "Name of the compute cluster to use in datacenter"
}

variable "jenkins_slave_labels" {
  default     = "silverback"
  description = "Labels for jenkins slave"
}

variable "vsphere_location" {
  default     = "DICE/dre-terraform-nodes/fb1_ps_nodes"
  description = "What folder should the VMs be placed in on vSphere"
}

variable "domain_name" {
  default     = "dice.ad.ea.com"
  description = "what domain should the machine join"
}

variable "domain_ou" {
  default     = "OU=BuildMonkeys,OU=Computers,OU=Stockholm,OU=Offices,DC=dice,DC=ad,DC=ea,DC=com"
  description = "The organisational unit that machines should join"
}

variable "hardware_version" {
  type        = number
  default     = 19
  description = "The hardware version for VMs in this project"
}

variable "disk_size" {
  default     = "700"
  description = "size of the disk required in GB e.g. 250 = 250GB for C:"
}

# *************************************************************
# Environmental Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "vsphere_server" {}

variable "vsphere_user" {}

variable "vsphere_password" {}

variable "local_username" {}

variable "local_password" {}

variable "domain_password" {}

variable "domain_admin" {}

variable "project_dir" {}

variable "ARTIFACTORY_USER" {}

variable "ARTIFACTORY_PASSWORD" {}

variable "ARTIFACTORY_URL" {}

variable "commit_sha" {}

variable "commit_user" {}

variable "commit_url" {}
