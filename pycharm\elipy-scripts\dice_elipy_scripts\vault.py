"""
vault.py

<PERSON>t to handle vaulting of the builds and symbols that will
be released. We do this by copying the builds and symbols to
a network share location. We run an md5 check to make sure nothing
was corrupted. Symbols (only for retail config) are uploaded to a live symbol service and
marked as permanent. There is also some validation done, we check that
builds are in shift and that files listed in elipy_config exist in the vault.

General setup:
    - Set which platforms to run the vaulting process for.
        - This is a bit hardcoded for the current platforms,
          and needs to be expanded with gen 6.
    - Set destination path.
        - Construct it first, if none have been specified.
    - For vaulting build:
        - Set source path.
            - Construct it first, if none have been specified.
        - If we have Shift settings specified, check that the
          build we try to vault exists in Shift.
        - Run the vault.vault_build method from Elipy core.
    - For vaulting symbols:
        - Set source path.
        - Set destination path.
        - Run the vault.vault_symbols method from Elipy core.
    - Only build and symbols are defined as types,
      the script fails is any other type is specified.
    - Mark symbols with permanence (what's this?).
"""

import os
import click
import sys
from typing import List, Union
import yaml
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.data_build_utils import run_expression_debug_data
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import (
    build_metadata_utils,
    frostbite_core,
    filer_paths,
    LOGGER,
    SETTINGS,
    shift_utils,
    vault,
    running_processes,
)
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.exceptions import ELIPYException

SUPPORTED_PLATFORMS = ["ps4", "win64", "xb1", "server", "linuxserver", "ps5", "xbsx"]


@click.command("vault", short_help="Vaults a given build.")
@click.option(
    "--vault-type",
    required=True,
    help="What to vault. Choice are build and/or symbols.",
    type=click.Choice(["build", "symbols"]),
    multiple=True,
)
@click.option("--source", default=None, help="Fully qualified source path.")
@click.option("--destination", default=None, help="Fully qualified destination path.")
@click.option(
    "--platform",
    help="Platform for vaulting.",
    required=True,
    type=click.Choice(
        SUPPORTED_PLATFORMS + ["all", "all-but-gen5", "all-but-gen4", "all-but-win64"]
    ),
)
@click.option(
    "--build-url",
    required=False,
    help="Used to store vaulting logs. the URL of the currently running build.",
)
@click.option(
    "--build-id",
    required=False,
    help="Used to store vaulting logs. the UID of the currently running build.",
)
@click.option("--code-branch", help="Branch/stream to fetch the code/binary build from.")
@click.option("--code-changelist", help="Changelist of binaries to fetch.")
@click.option("--data-branch", help="Branch/stream that data is coming from.")
@click.option("--data-changelist", help="Changelist of data being used.")
@click.option(
    "--expression-debug-data",
    is_flag=True,
    help="Export expression debug data for vault.",
)
@click.option("--version", help="Version to store under.")
@click.option("--shift-user", help="User for Shift authentication", default=None)
@click.option("--shift-password", help="Password for Shift authentication.", default=None)
@click.option(
    "--use-elipy-shift-config",
    is_flag=True,
    help="Use shift elipy config instead of shift template.",
)
@click.option("--archive-builds-in-shift", is_flag=True, help="Archive builds in shift.")
@click.option("--md5-validation", is_flag=True, help="Validate vault with md5.")
@click.option(
    "--win64-trial/--no-win64-trial",
    default=True,
    help="Include by default win64-trial platform on symbol.",
)
@click.option("--include-servers", default="true", help="DEPRECATED")
@click.option(
    "--verify-post-vault",
    is_flag=True,
    help="Verifies that files have been properly vaulted in \
                  accordance with the vault_validation_config yaml file.",
)
@click.option(
    "--vault-layout",
    default="default",
    help="Targets a specific vault_validation_config in the case where multiple configurations \
              are defined in the vault_validation_config yaml file.",
)
@click.option("--build-location", default=None, help="Location of the build to vault.")
@click.option(
    "--vault-verification-location", default=None, help="Location of the vault verification file."
)
@click.option(
    "--dry-run",
    is_flag=True,
    help="Dry run the vaulting process.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    vault_type,
    source,
    destination,
    platform,
    build_url,
    build_id,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    expression_debug_data,
    version,
    shift_user,
    shift_password,
    use_elipy_shift_config,
    archive_builds_in_shift,
    md5_validation,
    win64_trial,
    include_servers,
    verify_post_vault,
    vault_layout,
    build_location,
    vault_verification_location,
    dry_run,
):
    """
    Vaults a given build.
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    if "--include-servers" in sys.argv:
        LOGGER.warning("'--include-servers {}' is deprecated.".format(include_servers))

    running_processes.kill()
    exceptions = []

    if not build_location:
        build_location = None
    if not vault_verification_location:
        vault_verification_location = None

    # Set platforms to vault.
    build_platforms = get_build_platforms_to_vault(platform)
    symbol_platforms = build_platforms
    if "win64" in symbol_platforms and win64_trial:
        symbol_platforms = symbol_platforms + ["win64-trial"]

    for current_platform in build_platforms:
        if "build" not in vault_type:
            continue

        LOGGER.info("Vaulting build for platform: {}".format(current_platform))
        try:
            destination_path = destination or set_destination_path(
                version, current_platform, data_changelist, code_changelist
            )

            source_path = source
            if not source:
                source_path = filer_paths.get_frosty_base_build_path(
                    data_branch=data_branch,
                    data_changelist=data_changelist,
                    code_branch=code_branch,
                    code_changelist=code_changelist,
                    platform=current_platform,
                    location=build_location,
                )

            if shift_user and shift_password:
                shift_check(
                    source=source_path,
                    code_changelist=code_changelist,
                    data_changelist=data_changelist,
                    code_branch=code_branch,
                    data_branch=data_branch,
                    shift_user=shift_user,
                    shift_password=shift_password,
                    use_elipy_shift_config=use_elipy_shift_config,
                    archive=archive_builds_in_shift,
                )

            vault.vault_build(
                source=source_path,
                destination=destination_path,
                platform=current_platform,
                build_url=build_url,
                build_id=build_id,
                md5_validation=md5_validation,
                dry_run=dry_run,
            )

            if expression_debug_data and not dry_run:
                source_path_expression_data = filer_paths.get_expression_debug_data_path(
                    data_branch,
                    data_changelist,
                    code_branch,
                    code_changelist,
                    current_platform,
                )
                run_expression_debug_data(
                    code_changelist,
                    data_changelist,
                    code_branch,
                    data_branch,
                    current_platform,
                    source_path_expression_data,
                    os.path.join(destination_path, "ExpressionDebugData"),
                )
        except Exception as exce:
            LOGGER.error("Failed to Vault {0} build".format(current_platform), exc_info=True)
            exceptions.append(exce)

    for current_platform in symbol_platforms:
        if "symbols" not in vault_type:
            continue

        LOGGER.info("Vaulting symbols for platform: {}".format(current_platform))
        try:
            destination_path = destination or set_destination_path(
                version, current_platform, data_changelist, code_changelist
            )
            sym_path = None
            if SETTINGS.get("vault_symstore"):
                sym_path = get_symstore_vault_destination(current_platform)

            args = {
                "source": source,
                "vault_destination": os.path.join(destination_path, "symbol"),
                "symstore_vault_destination": sym_path,
                "build_url": build_url,
                "build_id": build_id,
                "platform": current_platform,
                "change_list": code_changelist,
                "code_branch": code_branch,
                "product_name": f"{current_platform}.{code_branch}.{data_branch}",
                "md5_validation": False,
                "dry_run": dry_run,
                "location": build_location,
            }

            vault.vault_symbols(**args)  # pylint: disable=unexpected-keyword-arg

            # Vault symbols from combined builds non-target stream
            source_path = source
            if not source:
                source_path = filer_paths.get_frosty_base_build_path(
                    data_branch=data_branch,
                    data_changelist=data_changelist,
                    code_branch=code_branch,
                    code_changelist=code_changelist,
                    platform=current_platform,
                    location=build_location,
                )

            vaulted_combined_builds = set()
            builds = get_builds_within_path(source_path)
            for build in builds:
                LOGGER.info("Found build: {}".format(build.id))
                if "combine" in build.source.get("package_type"):
                    frosty_path = build.id
                    components = frosty_path.split(os.sep)
                    combined_code_changelist = components[-1]
                    combined_code_branch = components[-2]
                    combined_data_changelist = components[-3]
                    combined_data_branch = components[-4]
                    combined_source_path = filer_paths.get_frosty_base_build_path(
                        data_branch=combined_data_branch,
                        data_changelist=combined_data_changelist,
                        code_branch=combined_code_branch,
                        code_changelist=combined_code_changelist,
                        platform=current_platform,
                    )

                    build_key = (
                        combined_code_changelist,
                        combined_code_branch,
                        combined_data_changelist,
                        combined_data_branch,
                    )

                    if build_key in vaulted_combined_builds:
                        LOGGER.info("Skipping already vaulted combined build: {}".format(build_key))
                        continue

                    LOGGER.info(
                        "Vaulting symbols for combined build: {}".format(combined_source_path)
                    )
                    args = {
                        "source": None,
                        "vault_destination": os.path.join(destination_path, "combined_symbol"),
                        "symstore_vault_destination": sym_path,
                        "build_url": build_url,
                        "build_id": build_id,
                        "platform": current_platform,
                        "change_list": combined_code_changelist,
                        "code_branch": combined_code_branch,
                        "product_name": f"{current_platform}.{combined_code_branch}."
                        f"{combined_data_branch}",
                        "md5_validation": False,
                        "dry_run": dry_run,
                        "location": build_location,
                    }
                    vault.vault_symbols(**args)
                    vaulted_combined_builds.add(build_key)

        except Exception as exce:
            LOGGER.error("Failed to Vault {0} symbols".format(current_platform), exc_info=True)
            exceptions.append(exce)

    if exceptions:
        raise Exception("Vaulting failed, please review log for details")

    if verify_post_vault:
        post_vaulting_verification(
            destination=destination,
            platforms=build_platforms,
            version=version,
            data_changelist=data_changelist,
            code_changelist=code_changelist,
            vault_layout=vault_layout,
            vault_verification_location=vault_verification_location,
        )


def set_destination_path(version, platform, data_changelist, code_changelist):
    """
    Set the destination path for vaulting.
    """
    destination_base = SETTINGS.get("vault_destination")
    return os.path.join(
        destination_base, version, platform, data_changelist + "_" + code_changelist
    )


def get_symstore_vault_destination(platform: str) -> str:
    """
    Get the destination for the symbol store.
    """
    sym_store_path = "{}" + SETTINGS.get(key="symbol_stores_suffix", default="")
    if "linux" in platform.lower():
        sym_dest_path = "linuxserver"
    elif "ps" in platform.lower():
        sym_dest_path = sym_store_path.format("ps")
    else:
        sym_dest_path = sym_store_path.format("ms")
    return os.path.join(
        SETTINGS.get("vault_destination"),
        "symbol_services_vault",
        sym_dest_path,
    )


def get_build_platforms_to_vault(platform: str) -> List[str]:
    """
    Sort out which platforms to vault.
    """
    LOGGER.info("Checking which platforms to vault: % s", platform)
    # Set platforms to back up baseline for.
    if platform == "all":
        platforms = SUPPORTED_PLATFORMS
    elif platform == "all-but-gen5":
        platforms = ["ps4", "win64", "xb1"]
    elif platform == "all-but-gen4":
        platforms = ["ps5", "win64", "xbsx"]
    elif platform == "all-but-win64":
        platforms = ["ps4", "ps5", "xb1", "xbsx"]
    else:
        platforms = [platform]
    LOGGER.info("Will vault: % s", platforms)
    return platforms


def shift_check(
    source: str,
    code_changelist: str,
    data_changelist: str,
    code_branch: str,
    data_branch: str,
    shift_user: str,
    shift_password: str,
    use_elipy_shift_config: bool,
    shift_file: str = "submit.shift",
    archive: bool = False,
):
    """
    Verify that builds are in shift.
    """
    _shift_utils = shift_utils.ShiftUtils(shift_user, shift_password)

    shift_builds = _shift_utils.find_builds(source)
    for build_path in shift_builds:
        (
            platform,
            config,
            frosty_format,
            region,
            content_layer,
        ) = _shift_utils.parse_frosty_filer_path(build_path)

        if use_elipy_shift_config:
            shift_data = _shift_utils.get_shift_data(
                code_changelist,
                code_branch,
                data_changelist,
                data_branch,
                platform,
                config,
                frosty_format,
                region,
                content_layer,
            )
        else:
            shift_data = _shift_utils.read_shift_file(build_path, shift_file)

        sku_id = shift_data["skuid"]
        shifted_builds = _shift_utils.get_builds_by_sku(sku_id)

        shift_build = None
        for build in shifted_builds:
            if code_changelist in build["Version"] and data_changelist in build["Version"]:
                shift_build = build
                LOGGER.info(
                    "Verified {}, {}, {}, {} is in shift".format(
                        platform, config, frosty_format, region
                    )
                )
                break

        if shift_build is None:
            raise ELIPYException("Could not find build in shift, verify that build is shifted.")
        if archive:
            build_id = shift_build["Build ID"]
            _shift_utils.set_retention_policy(build_id)
            LOGGER.info(
                "Archived {}, {}, {}, {} in shift".format(platform, config, frosty_format, region)
            )
    if not shift_builds:
        raise ELIPYException(
            "Could not find any shift builds in path, verify build is shifted and exists."
        )


def post_vaulting_verification(
    destination: str,
    platforms: List[str],
    version: str,
    data_changelist: str,
    code_changelist: str,
    vault_layout: str = "default",
    vault_verification_location: Union[str, None] = None,
):
    """
    Verify that files have been properly vaulted in accordance with the vault_layout.
    """

    config_path = get_config_path_post_vaulting_verification(
        vault_verification_location=vault_verification_location
    )
    contents = get_config_file_contents(config_path)
    destination_base = get_destination_base_post_vaulting_verification(destination)

    LOGGER.info("Vault destination base is: {}".format(destination_base))
    LOGGER.info("Using vault verification config: {}".format(config_path))

    error = 0
    platforms.insert(0, "root")
    for platform in platforms:
        try:
            vault_verification_config = get_target_vault_verification_config(
                contents, platform, vault_layout
            )

            for path in vault_verification_config["paths"]:
                path_error = _verify_single_path(
                    path,
                    vault_verification_config,
                    destination_base,
                    version,
                    data_changelist,
                    code_changelist,
                )
                error += path_error

        except KeyError as key_error:
            LOGGER.error(
                "[FAILURE] KeyError! Vault layout {} not in {} for platform {}.".format(
                    key_error, config_path, platform
                )
            )
            error += 1

    if error:
        raise ELIPYException(
            "Post vault verification failed - {0} error(s) have occurred.".format(error)
        )


def _search_file_in_nested_directories(
    path_to_check, path, vault_verification_config, replacements
):
    """
    Args:
        path_to_check: The full path that was originally checked
        path: The path configuration from vault verification config
        vault_verification_config: The vault verification configuration
        replacements: List of variable replacements to apply

    Returns:
        bool: True if file was found, False otherwise
    """
    file_found = False

    # Only apply to patch_combine paths with sufficient depth
    if "patch_combine" in path_to_check and len(path["path_location"]) > 3:
        # Extract the directory and filename
        dir_path = os.path.join(*vault_verification_config["base"], *path["path_location"][:-1])
        filename = path["path_location"][-1]

        # Apply variable replacements to directory path
        for replacement in replacements:
            dir_path = dir_path.replace(replacement[0], replacement[1])

        # Search recursively for the file
        if os.path.exists(dir_path):
            for root, _, files in os.walk(dir_path):
                if filename in files:
                    found_path = os.path.join(root, filename)
                    LOGGER.info("[SUCCESS] File found in nested directory: {}".format(found_path))
                    file_found = True
                    break

    return file_found


def _verify_single_path(
    path, vault_verification_config, destination_base, version, data_changelist, code_changelist
):
    """
    Verify a single path in the vault verification process.

    Args:
        path: The path configuration from vault verification config
        vault_verification_config: The vault verification configuration
        destination_base: Base destination path
        version: Version string
        data_changelist: Data changelist
        code_changelist: Code changelist

    Returns:
        int: Number of errors (0 or 1)
    """
    path_to_check = os.path.join(*vault_verification_config["base"], *path["path_location"])
    replacements = [
        ["$DESTINATION_BASEPATH$", destination_base],
        ["$VERSION$", str(version)],
        ["$DATACHANGELIST$", str(data_changelist)],
        ["$CODECHANGELIST$", str(code_changelist)],
    ]
    for replacement in replacements:
        path_to_check = path_to_check.replace(replacement[0], replacement[1])

    LOGGER.debug("Checking vault path: {}".format(path_to_check))

    if os.path.exists(path_to_check) and "min_items" in path:
        return _verify_path_min_items(path_to_check, path)
    if os.path.exists(path_to_check):
        LOGGER.info("[SUCCESS] File exists: {}".format(path_to_check))
        return 0
    return _handle_missing_path(path_to_check, path, vault_verification_config, replacements)


def _verify_path_min_items(path_to_check, path):
    """
    Verify that a path contains the minimum required number of items.

    Args:
        path_to_check: The full path to check
        path: The path configuration containing min_items

    Returns:
        int: Number of errors (0 or 1)
    """
    num_of_items = len(os.listdir(os.path.join(path_to_check)))
    message = ("{}: \n\tExpected min # of items: {}" "\n\tActual # of items found: {}").format(
        path_to_check, path["min_items"], num_of_items
    )
    if num_of_items >= path["min_items"]:
        LOGGER.info("[SUCCESS] Path: {}".format(message))
        return 0
    LOGGER.error("[FAILURE] Path: {}".format(message))
    return 1


def _handle_missing_path(path_to_check, path, vault_verification_config, replacements):
    """
    Handle a missing path by checking for nested directories and PDB errors.

    Args:
        path_to_check: The full path that was checked
        path: The path configuration from vault verification config
        vault_verification_config: The vault verification configuration
        replacements: List of variable replacements to apply

    Returns:
        int: Number of errors (0 or 1)
    """
    file_found = _search_file_in_nested_directories(
        path_to_check, path, vault_verification_config, replacements
    )

    if file_found:
        return 0

    # Check if this is a PDB file that might have failed to upload
    if path_to_check.endswith(".pdb"):
        ignore_pdb_errors = SETTINGS.get("ignore_pdb_errors", default="false").lower() == "true"
        if ignore_pdb_errors:
            LOGGER.warning("[WARNING] Missing PDB file ignored: {}".format(path_to_check))
            return 0

    LOGGER.error("[FAILURE] Missing in Vault: {}".format(path_to_check))
    return 1


def get_target_vault_verification_config(settings: dict, platform: str, vault_layout: str) -> dict:
    """
    Return target platform and vault_layout subset of vault_verification_config.

    settings: Dict with settings for vault verification
    platform: The target platform
    vault_layout: The target vault layout vault_verification_config
    """
    try:
        # Use default vault_layout for root platform if it's missing the passed vault_layout
        if platform == "root" and vault_layout not in settings["root"].keys():
            vault_reqs = settings[platform]["default"]
            used_vault_layout = "default"
        else:
            vault_reqs = settings[platform][vault_layout]
            used_vault_layout = vault_layout

        LOGGER.info("Using {}:{} in vault_verification_config".format(platform, used_vault_layout))

        return vault_reqs

    except KeyError as exc:
        if vault_layout in ["", None]:
            LOGGER.error(
                "'{}' is an invalid vault_layout. Must be a str which".format(vault_layout)
                + " exists in vault_verification_config for platform {}".format(platform)
            )
        raise exc


def get_config_path_post_vaulting_verification(
    vault_verification_location: Union[str, None] = None,
) -> str:
    """
    Helper function, returns config_path from the elipy_config yaml file.
    """
    vault_verification_config_path = SETTINGS.get(
        "vault_verification_config_path", location=vault_verification_location
    )
    elipy_config = os.environ.get("ELIPY_CONFIG")
    config_path = os.path.join(os.path.dirname(elipy_config), vault_verification_config_path)
    if not os.path.isfile(config_path):
        config_path = os.path.join(frostbite_core.get_game_root(), config_path)
    return config_path


def get_config_file_contents(config_path: str) -> dict:
    """
    Helper function, returns contents of the vault-verification config file in provided config_path
    """
    if os.path.isfile(config_path):
        with open(os.path.join(config_path), "r") as file_desc:
            contents = yaml.safe_load(file_desc)
    else:
        raise ELIPYException("Vault verification config file missing at: {}".format(config_path))

    return contents


def get_destination_base_post_vaulting_verification(destination_base: str) -> str:
    """
    Helper function, returns destination_base, either by default or from SETTINGS.
    """
    if destination_base is None:
        destination_base = SETTINGS.get("vault_destination")
    return destination_base


def get_builds_within_path(path: str) -> List[dict]:
    """
    Help function for finding all builds within the path in bilbo
    """
    matching_build_ids = []
    builds = []
    bilbo = build_metadata_utils.setup_metadata_manager()
    if os.path.isdir(path):
        matching_builds = bilbo.get_builds_matching(path)
        matching_build_ids = [build.id for build in matching_builds if "bundles" not in build.id]

    LOGGER.info("Found matching build ids: {}".format(matching_build_ids))
    for build_id in matching_build_ids:
        builds.extend(bilbo.get_build_by_id(build_id))

    return builds
