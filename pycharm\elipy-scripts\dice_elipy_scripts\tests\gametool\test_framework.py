"""
test_framework.py

Unit testing for framework
"""

import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from dice_elipy_scripts.gametool.framework import cli


@patch("dice_elipy_scripts.gametool.gametool.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.gametool.framework.running_processes.kill", MagicMock())
@patch(
    "dice_elipy_scripts.gametool.framework.frostbite_core.get_tnt_root",
    MagicMock(return_value="tnt_root"),
)
class TestFramework(unittest.TestCase):
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_CLEAN = "--clean"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_P4_USER = "--p4-user"
    OPTION_SUBMIT = "--submit"

    VALUE_CODE_CHANGELIST = "1234"
    VALUE_CLEAN = "true"
    VA<PERSON>UE_P4_PORT = "perforce:1666"
    VALUE_P4_CLIENT = "test_client"
    VALUE_P4_USER = "test_user"
    VALUE_SUBMIT = "true"

    DEFAULT_ARGS = [
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_CLEAN,
        VALUE_CLEAN,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
        OPTION_P4_USER,
        VALUE_P4_USER,
        OPTION_SUBMIT,
        VALUE_SUBMIT,
    ]

    @patch("dice_elipy_scripts.gametool.framework.p4.P4Utils")
    @patch("dice_elipy_scripts.gametool.framework.fbcli")
    def test_framework(self, mock_fbcli, mock_p4_utils):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        mock_p4_utils.assert_called_once_with(
            port=self.VALUE_P4_PORT, user=self.VALUE_P4_USER, client=self.VALUE_P4_CLIENT
        )
        mock_fbcli.run.assert_has_calls(
            [
                unittest.mock.call("cleansln", ["framework"]),
                unittest.mock.call("buildsln", ["framework"]),
            ]
        )
        mock_p4_utils.return_value.submit.assert_called_once_with(
            message=f"[Automated] Submitting rebuilt Framework binaries from cl {self.VALUE_CODE_CHANGELIST}"
        )
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.gametool.framework.p4.P4Utils")
    @patch("dice_elipy_scripts.gametool.framework.fbcli")
    def test_framework_throw_exception_upon_submit(self, mock_fbcli, mock_p4_utils):
        error_message = "Submit failed"
        mock_p4_utils.return_value.submit.side_effect = Exception(error_message)
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)

        assert result.exit_code == 1
        assert error_message in str(result.exception)
