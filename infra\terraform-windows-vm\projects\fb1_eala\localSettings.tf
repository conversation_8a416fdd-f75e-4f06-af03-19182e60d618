#*************************************************************
#  Sets up the initial needs to provide a vsphere instance
#*************************************************************
# Point to our provider
# https://www.terraform.io/docs/providers/vsphere/index.html
# *************************************************************
terraform {
  required_version = ">= 1.0.0"
  required_providers {
    vsphere = {
      version = "= 2.12.0" #locking Version to 2.12.0 to avoid introducing breaks
    }
    random = {
      source  = "hashicorp/random"
      version = "= 3.0.0"
    }
  }
}
provider "vsphere" {
  user           = var.vsphere_user
  password       = var.vsphere_password
  vsphere_server = "eala-vcenter.la.ad.ea.com"
  # if you have a self-signed cert or not
  allow_unverified_ssl = true
  persist_session      = true
}

# *************************************************************
# Default Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "cloning_timeout" {
  type        = string
  default     = "120"
  description = "Amount of time in minutes to wait for vm's to be cloned.  must be at least 10 minutes."
}
variable "network" {
  default     = "VLAN32 ***********/22"
  description = "Name of the network name to use in datacentre"
}
variable "datacenter" {
  default     = "Los Angeles"
  description = "The name of vcentre name to use e.g. DICE "
}
variable "project_name" {
  default     = "fb1_eala"
  description = "namer of the project e.g. diceupgradenext, walrus ect"
}
variable "disk_size" {
  default     = "700"
  description = "size of the disk required in GB e.g. 700 = 700GB for C:"
}
variable "packer_template" {
  type        = string
  default     = "win10_22H2-cobra-v1.1101.0b76e848"
  description = "Packer template for new vms. Existing vms will not be updated"
}
variable "domain_name" {
  type        = string
  default     = "la.ad.ea.com"
  description = "domain name area that the node will get installed to"
}
variable "cluster_name" {
  default     = "DICE Build NFS"
  description = "Name of the datastore cluster to use in datacenter"
}
variable "hardware_version" {
  type        = number
  default     = 15
  description = "The hardware version for VMs in this project"
}
variable "local_admin_group" {
  type        = string
  default     = "AD\\COBRALEGACY.SECURITY.BUILD.AGENTS"
  description = "Local admin group used for ansible and jenkins authentication"
}

# *************************************************************
# Environmental Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "vsphere_server" {}

variable "vsphere_user" {}

variable "vsphere_password" {}

variable "local_username" {}

variable "local_password" {}

variable "domain_admin" {}

variable "domain_password" {}

variable "project_dir" {}

variable "ARTIFACTORY_USER" {}

variable "ARTIFACTORY_PASSWORD" {}

variable "ARTIFACTORY_URL" {}

variable "commit_sha" {}

variable "commit_user" {}

variable "commit_url" {}
