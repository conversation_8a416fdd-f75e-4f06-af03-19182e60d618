import argparse
import json
import logging
import logging.config
import os
import re
from glob import glob
from textwrap import indent

import sentry_sdk
import vsphere_tools

# This error is defined on the gitlab ci to allow this failure
ERROR_CODE_PIPELINES_WARNING = 557799


def setup_logging():
    """Setup logging configuration"""

    logging.config.dictConfig(
        {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "simple": {
                    "format": "%(asctime)s %(name)s [%(levelname)s]: %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S",
                },
            },
            "handlers": {
                "console": {"class": "logging.StreamHandler", "formatter": "simple"},
            },
            "loggers": {
                "": {
                    "handlers": ["console"],
                    "level": os.environ.get("LOG_LEVEL", "INFO"),
                    "propagate": True,
                }
            },
        }
    )


setup_logging()
LOGGING = logging.getLogger(__name__)


def main(project, vc_pass, vc_user, vc_host, node_info_file):
    """Main Function that ensures attributes are up to date"""

    # Save error without breaking full script
    errors = []
    warnings = []

    # Initiate sentry
    sentry_sdk.init(os.environ["SENTRY_DSN"])

    # Setting up Vcenter Connection using dre_vsphere_tools
    vclient = vsphere_tools.VsphereClient(hostname=vc_host, username=vc_user, password=vc_pass)

    inventory_path = os.path.join("windows", "inventories")
    group_vars_path = os.path.join("windows", "group_vars")
    master_inventory_path = os.path.join("windows", "master_inventory")
    if not os.path.exists(master_inventory_path):
        os.mkdir(master_inventory_path)
    masters = {}
    git_runners = False

    # Get values from project config json
    main_project_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))

    # Check to ensure correct pathing is used for projects
    project_json_path = os.path.join(main_project_path, "projects", project, project + ".json")
    if re.search(r"runners", project):
        git_runners = True
        project_json_path = os.path.join(
            main_project_path, "projects", "gitlab_runners", project, project + ".json"
        )

    LOGGING.info("Opening config file: `{}`".format(project_json_path))
    with open(project_json_path) as json_file:
        data = json.load(json_file)

    matching_vms = {}
    vsphere_content = vclient.get_vsphere_content()
    vm_list = vclient.get_vms_from_node_info(node_info_file, return_json=True)
    LOGGING.info("Found {} VM/s in state file".format(len(vm_list)))

    ansible_commit_hash = os.environ.get("ANSIBLE_COMMIT_HASH", None)
    force_run_ansible_all = os.environ.get("FORCE_RUN_ANSIBLE_ALL") == "yes"
    force_run_ansible_vms = [
        x.strip() for x in os.environ.get("FORCE_RUN_ANSIBLE_VMs", "").split(",")
    ]

    LOGGING.info(f"ANSIBLE_COMMIT_HASH: {ansible_commit_hash}")
    LOGGING.info(f"FORCE_RUN_ANSIBLE_ALL: {force_run_ansible_all}")
    LOGGING.info(f"FORCE_RUN_ANSIBLE_VMs: {force_run_ansible_vms}")

    # Getting VMs Set Custom Attributes and comparing them against terraform_custom_attributes
    # Updating them if they do not match terraform_custom_attributes.
    for vm_item in vm_list:
        try:
            vm_uuid = vm_item.get("id", None)
        except Exception as e:
            warnings.append({"vm": vm_item["name"], "msg": str(e)})

        try:
            run_ansible = False
            terraform_custom_attributes = vm_item.get("custom_attributes", {}).items()
            run_ansible = True if not terraform_custom_attributes else False
            vm_attributes = vclient.get_specified_nodes_custom_attributes(
                content=vsphere_content,
                vm_id=vm_uuid,
                vm_name=vm_item["name"],
                no_atrribute_id=True,
            )
            LOGGING.info(f"Checking [{vm_item['name']}] with UUID [{vm_item['id']}]")

            for terra_name, terra_value in terraform_custom_attributes:
                if vm_attributes.get(terra_name, {}).get(terra_name) != terra_value:
                    # Updating Key if the values don't match to match what's in terraform.
                    vclient.set_specified_nodes_custom_attributes(
                        vm_id=vm_uuid,
                        vm_name=vm_item["name"],
                        attribute_key=terra_name,
                        attribute_value=terra_value,
                        content=vsphere_content,
                    )
                    run_ansible = True

            # checking if vm in production has the same commit hash as ansible/windows repo
            vm_ansible_commit = vm_attributes.get("Ansible_Commit", {}).get("Ansible_Commit")

            has_latest_ansible_commit = vm_ansible_commit == ansible_commit_hash

            vm_was_specified = vm_item["name"] in force_run_ansible_vms
            if (
                run_ansible
                or force_run_ansible_all
                or not has_latest_ansible_commit
                or vm_was_specified
            ):
                reasons = []
                if run_ansible:
                    reasons.append("Needs to update custom_attributes")
                if force_run_ansible_all:
                    reasons.append("FORCE_RUN_ANSIBLE_ALL is set")
                if not has_latest_ansible_commit:
                    reasons.append("Ansible commit hash is outdated")
                if vm_was_specified:
                    reasons.append("VM specified in FORCE_RUN_ANSIBLE_VMs")

                LOGGING.info(
                    f"         Will run Ansible on [{vm_item['name']}]. Reasons: {', '.join(reasons)}"
                )
                matching_vms[vm_uuid] = vm_item["name"]
        except Exception as e:
            if "has already been deleted or has not been completely created" in str(e):
                LOGGING.warning(
                    "Skipping {} and continue with the rest of machines".format(vm_item["name"])
                )
                warnings.append({"vm": vm_item["name"], "msg": str(e)})
            else:
                errors.append({"vm": vm_item["name"], "msg": str(e)})

    LOGGING.info("Found {} matching VM/s in vCenter".format(len(matching_vms)))
    LOGGING.info("Cleaning up Ansible inventory {}".format(inventory_path))

    for _file in os.listdir(inventory_path):
        os.remove(os.path.join(inventory_path, _file))

    for vm_uuid, vm_name in matching_vms.items():

        if git_runners:
            # All Git_Runners connect to gitlab
            vm_master_url = "https://i_am_a_fake_url.ea.com/"
            vm_label = "None"
            vm_domain = runner_domain_checker(vc_host)

        else:
            vm_attributes = None
            try:
                vm_attributes = vclient.get_specified_nodes_custom_attributes(
                    content=vsphere_content,
                    vm_id=vm_uuid,
                    vm_name=vm_name,
                    no_atrribute_id=True,
                )

            # Skipping if the VM is not found
            except Exception as e:
                errors.append({"vm": vm_name, "error": str(e)})
                continue

            if (
                next(
                    key
                    for key in vm_attributes
                    if key == next(iter(vm_attributes["Jenkins_Master"]))
                )
                == "Jenkins_Master"
            ):
                vm_master_url = vm_attributes.get("Jenkins_Master", {}).get("Jenkins_Master")
                vm_master_url = vm_master_url.replace("http://", "https://")
            if (
                next(
                    key
                    for key in vm_attributes
                    if key == next(iter(vm_attributes["Jenkins_Labels"]))
                )
                == "Jenkins_Labels"
            ):
                vm_label = vm_attributes.get("Jenkins_Labels", {}).get("Jenkins_Labels")
            if (
                next(
                    key for key in vm_attributes if key == next(iter(vm_attributes["Domain_Name"]))
                )
                == "Domain_Name"
            ):
                vm_domain = vm_attributes.get("Domain_Name", {}).get("Domain_Name")

            if is_already_in_inventory(inventory_path, vm_name):
                LOGGING.info(" '{}' is already configured in ansible, skipping vm".format(vm_name))
                continue

        jenkins_master = vm_master_url.split(".")[0].split("//")[-1]
        inventory_file = os.path.join(inventory_path, jenkins_master + "-silverback.inventory")

        group_var_dir = os.path.join(group_vars_path, jenkins_master + "-silverback")
        os.makedirs(group_var_dir, exist_ok=True)
        group_vars_file = os.path.join(group_var_dir, "silverback_vars")
        master_inventory_file = os.path.join(master_inventory_path, jenkins_master + "-silverback")

        if vm_master_url not in masters.keys():
            with open(inventory_file, "w") as _file:
                LOGGING.info("Writing new {}".format(inventory_file))
                _file.write("[{}-silverback]\n".format(jenkins_master))
            with open(group_vars_file, "w") as _file:
                _file.write("---\njenkins_master_url: {}\n".format(vm_master_url))
            with open(master_inventory_file, "w") as _file:
                _file.write("Marker for ansible run")
            masters[vm_master_url] = jenkins_master

        with open(inventory_file, "a") as _file:
            _file.write('{}.{} jenkins_agent_labels="{}"\n'.format(vm_name, vm_domain, vm_label))

    for _, jenkins_master in masters.items():
        inventory_file = os.path.join(
            inventory_path, "{}-silverback.inventory".format(jenkins_master)
        )
        group_var_dir = os.path.join(group_vars_path, jenkins_master + "-silverback")
        os.makedirs(group_var_dir, exist_ok=True)
        group_vars_file = os.path.join(group_var_dir, "silverback_vars")
        with open(inventory_file, "a") as _file:
            _file.write("[{}:children]\n".format(data["ansible_project_vars"]))
            _file.write("{}-silverback\n".format(jenkins_master))

        with open(inventory_file, "r") as _file:
            LOGGING.info(
                "Content of inventory file for {}:\n{}".format(
                    jenkins_master, indent(_file.read(), "\t")
                )
            )

        with open(group_vars_file, "r") as _file:
            LOGGING.info(
                "Content of group_vars file for {}:\n{}".format(
                    jenkins_master, indent(_file.read(), "\t")
                )
            )

    # print warnings only
    if warnings:
        LOGGING.warning("There were warnings on specific VMs:")

        for warning in warnings:
            LOGGING.warning("VM: {} - Warning: {}".format(str(warning["vm"]), str(warning["msg"])))

    # print and exit if there are errors
    if errors:
        LOGGING.error("There were errors on specific VMs:")

        for error in errors:
            LOGGING.error("VM: {} - Error: {}".format(str(error["vm"]), str(error["msg"])))

        raise Exception(
            "There were errors on specific VMs, please check the logs for more details."
        )


def is_already_in_inventory(inventory_folder_path, name):
    if not os.path.exists(inventory_folder_path):
        raise IOError("Can not find path {}".format(inventory_folder_path))

    file_patten = os.path.join(inventory_folder_path, "*.inventory")
    data = []
    for file_path in glob(file_patten):
        with open(file_path) as f:
            data += f.readlines()

    match_found = False
    for line in data:
        if line.startswith(name):
            match_found = True
            break

    return match_found


def runner_domain_checker(vc_host):
    """
    Ensure correct domain is written out for when runners are
    being proccessed currently only used for making gitlab runnners.
    """
    vm_domain = ""
    try:
        if vc_host == "vc.dice.ad.ea.com":
            vm_domain = "dice.ad.ea.com"
        elif vc_host == "oh-vcenter1.ad.ea.com":
            vm_domain = "dice.ad.ea.com"
        elif vc_host == "eala-vcenter.la.ad.ea.com":
            vm_domain = "la.ad.ea.com"
        else:
            raise ValueError
        return vm_domain
    except Exception as exc:
        LOGGING.error(" '{}' : is an invalid or non-declared host name".format(vc_host))
        raise exc


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--project", required=True, help="project name for finding project json file")
    ap.add_argument("--vc_pass", required=True, help="vcenter credentials")
    ap.add_argument("--vc_user", required=True, help="vcenter user")
    ap.add_argument("--vc_host", required=True, help="vcenter hostname")
    ap.add_argument("--node_info_file", required=True, help="list of nodes and ids from terraform")
    args = vars(ap.parse_args())

    main(
        project=args["project"],
        vc_pass=args["vc_pass"],
        vc_user=args["vc_user"],
        vc_host=args["vc_host"],
        node_info_file=args["node_info_file"],
    )
