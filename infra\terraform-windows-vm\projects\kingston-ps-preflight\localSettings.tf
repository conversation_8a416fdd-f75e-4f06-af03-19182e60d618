#*************************************************************
#  Sets up the initial needs to provide a vsphere instance
#*************************************************************
# Point to our provider
# https://www.terraform.io/docs/providers/vsphere/index.html
# *************************************************************
terraform {
  required_version = ">= 1.0.0"
  required_providers {
    vsphere = {
      version = "= 2.12.0" #locking Version to 2.12.0 to avoid introducing breaks
    }
    random = {
      source  = "hashicorp/random"
      version = "= 3.0.0"
    }
  }
}
provider "vsphere" {
  user           = var.vsphere_user
  password       = var.vsphere_password
  vsphere_server = var.vsphere_server
  # if you have a self-signed cert or not
  allow_unverified_ssl = true
  persist_session      = true
}

# *************************************************************
# Default Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "kingston_network" {
  default     = "Buildfarm (VLAN 1028)"
  description = "Name of the network name to use in datacentre"
}
variable "kingston_datacenter" {
  default     = "DICE"
  description = "The name of vcentre name to use e.g. DICE "
}
variable "kingston_compute_cluster" {
  default     = "DICE-BUILD-PS"
  description = "Name of the compute cluster to use in datacenter"
}
variable "project_name" {
  default     = "kingston-ps-preflight"
  description = "namer of the project e.g. diceupgradenext, walrus ect"
}
variable "disk_size" {
  default     = "700"
  description = "size of the disk required in GB e.g. 700 = 700GB for C:"
}
variable "packer_template" {
  type        = string
  default     = "win10_22H2-cobra-v1.1101.0b76e848_PS"
  description = "Packer template for new vms. Existing vms will not be updated"
}
variable "vsphere_location" {
  type        = string
  default     = "DICE/dre-terraform-nodes/kingston_ps_nodes"
  description = "VSphere location that VMs should be put once built"
}
variable "domain_name" {
  type        = string
  default     = "dice.ad.ea.com"
  description = "domain name area that the node will get installed to"
}
variable "kin_domain_ou" {
  type        = string
  default     = "OU=Kingston,OU=BuildMonkeys,OU=Computers,OU=Stockholm,OU=Offices,DC=dice,DC=ad,DC=ea,DC=com"
  description = "The AD organisational area the node is to be placed"
}
variable "hardware_version" {
  type        = number
  default     = 19
  description = "The hardware version for VMs in this project"
}

# *************************************************************
# Environmental Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "vsphere_server" {}

variable "vsphere_user" {}

variable "vsphere_password" {}

variable "local_username" {}

variable "local_password" {}

variable "domain_password" {}

variable "domain_admin" {}

variable "project_dir" {}

variable "ARTIFACTORY_USER" {}

variable "ARTIFACTORY_PASSWORD" {}

variable "ARTIFACTORY_URL" {}

variable "commit_sha" {}

variable "commit_user" {}

variable "commit_url" {}
