---
description: A reminder for the agent managing project discovery to synthesize information and transition to planning after extensive information gathering, as per APM protocol. This rule is intended for use if Cursor IDE's rule functionality is active.
globs: 
alwaysApply: false
---
## APM Discovery Synthesis & Transition Reminder

**Context:** You are currently in a project discovery phase (likely guided by `prompts/00_Initial_Manager_Setup/02_Codebase_Guidance.md`). You may have just processed significant new information.

**Immediate Goal Reminder:**
1.  **Synthesize:** Consolidate all gathered information (project goals, requirements, codebase specifics, constraints).
2.  **Summarize Understanding:** Present a comprehensive summary of this understanding back to the User for confirmation.
3.  **Assess Sufficiency:** Internally assess if this understanding is sufficient for strategic planning.
4.  **Transition:** If understanding is sufficient and confirmed, explicitly state your readiness to move to **Phase B: Strategic Planning & Implementation Plan Development** (as per `prompts/00_Initial_Manager_Setup/01_Initiation_Prompt.md`).

**Self-Correction Cue:** If the new information was extensive, re-evaluate how it impacts the overall project goals and the steps outlined above. Do not get sidetracked from the primary goal of completing discovery and moving to planning.
