@echo off

:: <PERSON><PERSON><PERSON> for installing and setting up the environment needed to run elipy.
:: Dependent on running in an fb environment.
:: It will install virtualenv in the current Python path, create a virtual environment, activate it,
:: set the yml path if needed, and install elipy and studio script packages from PyPI.

:: Can be run by the following command:
:: TnT\Bin\fbcli\cli.bat win64 && path\to\this\script\setup.bat
:: Path to script can either be absolute or relative to tnt root.

:: Variables that need updating are the yaml file name (yml_file_name) and the studio script package name (studio_script_pkg).
:: If studio scripts are not installed as a package, elipy config path needs replacing and the studio script pip command removed.

echo [INFO] [%date%, %time%] Validating input...
echo.

if "%1"=="" (
    echo "Needs yaml file name as parameter"
    exit /b 0
)

if NOT "%~2"=="" (
    set studio_script_version_spec===%~2
) else (
    set studio_script_version_spec=
)

if NOT "%~3"=="" (
    set elipy_version_spec===%~3
) else (
    set elipy_version_spec=
)

if NOT "%4"=="" (
    set target_pip_version=%4
) else (
    set target_pip_version=22.1
)

echo [INFO] [%date%, %time%] Setting up environment variables...
echo.

set studio_script_pkg=dice_elipy_scripts%studio_script_version_spec%
set elipy_pkg=elipy2%elipy_version_spec%
set yml_file_name=%1

set p4_py_dir=%GAME_ROOT%\tnt\bin\python
set virtual_dir=%GAME_ROOT%\Python\virtual
set elipy_py_scripts=%virtual_dir%\Scripts
set pip_cache_dir=%workspace_root%\.pip-cache
if "%AF2_USER%"=="" goto use_artifactory1
if "%AF2_TOKEN%"=="" goto use_artifactory1

echo [INFO] [%date%, %time%] AF2_USER and AF2_TOKEN set, using authenticated artifactory2 repository
set trusted_host=artifacts.ea.com

:: url encode the @ char in AF2_USER
for /f "usebackq delims=" %%A in (`python -c "import sys;print(sys.argv[1].replace('@','%%40'))" "%AF2_USER%"`) do set "AF2_USER=%%A"

set PIP_INDEX_URL=https://%AF2_USER%:%AF2_TOKEN%@%trusted_host%/artifactory/api/pypi/dre-pypi-federated/simple
set PIP_EXTRA_INDEX_URL=https://%AF2_USER%:%AF2_TOKEN%@%trusted_host%/artifactory/api/pypi/index-pypi-remote/simple
goto end

:use_artifactory1
echo [INFO] [%date%, %time%] AF2_USER or AF2_TOKEN not set, attempting to use artifactory1 repository
set trusted_host=artifacts.at.ea.com
set PIP_INDEX_URL=https://%trusted_host%/artifactory/api/pypi/dreeu-pypi-virtual/simple

:end
echo [INFO] [%date%, %time%] Environment variables set...

:: pip settings
:: set PIP_LOG=%workspace_root%\dev\logs\%~n0-verbose.log

:: Delete empty folders in Python dir, since those mess with pip. (Folders get left there empty by switching streams.)
echo [INFO] [%date%, %time%] Deleting empty folders in Python dir...
echo.
for /f "delims=" %%d in ('dir %p4_py_dir% /s /b /ad ^| sort /r') do rd "%%d" 2> NUL

echo [INFO] [%date%, %time%] Installing and setting up virtualenv...
echo.
python -m pip install --trusted-host %trusted_host% virtualenv<=20.23.0 setuptools>=60.5.0
python -m virtualenv --system-site-packages %virtual_dir%

if "%ELIPY_CONFIG%" == "" (
    set ELIPY_CONFIG=python\virtual\Lib\site-packages\%studio_script_pkg%\yml\%yml_file_name%
)

echo [INFO] [%date%, %time%] Calling `%elipy_py_scripts%\activate`...
echo.
set path=%path%;%elipy_py_scripts%
call %elipy_py_scripts%\activate

echo [INFO] [%date%, %time%] Installing PIP...
python -m pip --timeout 60 install --upgrade pip>=%target_pip_version% --disable-pip-version-check --cache-dir %pip_cache_dir%

echo [INFO] [%date%, %time%] Installing Studio Scripts...
python -m pip --timeout 60 install --upgrade %studio_script_pkg% --disable-pip-version-check --cache-dir %pip_cache_dir%

echo [INFO] [%date%, %time%] Installing %elipy_pkg%...
echo.
python -m pip --timeout 60 install --upgrade %elipy_pkg% --cache-dir %pip_cache_dir%

echo [INFO] [%date%, %time%] Done!
