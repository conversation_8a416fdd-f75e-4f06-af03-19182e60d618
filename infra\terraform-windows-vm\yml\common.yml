stages:
  - validate
  - fetch-configs
  - plan
  - apply
  - attache
  - sync
  - ansible
  - generate-docs

default:
  tags:
    - glaas-shared-k8s

variables:
  dockerterraform: registry.gitlab.ea.com/dre-cobra/container-images/python-terraform:python_3.7-terraform_1.12.0-commit_e0bcb386
  dockercontroller: $CI_REGISTRY/dre-cobra/ansible/docker-controller:1.3.28872371
  dockerpowershell: mcr.artifacts.ea.com/powershell:lts-7.4-alpine-3.17
  GIT_STRATEGY: clone
  FOREMAN_SSL_CERT: ./ansible/ca.pem
  FOREMAN_SSL_KEY: ./ansible/ca.key
  ANSIBLE_CONFIG: ./ansible.cfg
  ANSIBLE_FORKS: 200 # Global Ansible forks limit, this can be overridden in each project
  VAULT_RATE_LIMIT: 500
  VAULT_SERVER_URL: https://ess.ea.com
  VAULT_NAMESPACE: cds-dre-prod
  VAULT_AUTH_ROLE: "gl-dre-cobra-silverback"
  VAULT_AUTH_PATH: "jwt/gitlab"
  GIT_DEPTH: 1
  GET_SOURCES_ATTEMPTS: 3
  TF_IN_AUTOMATION: "true"

#***********************************************
#                   NOTES
# Ensure when creating new project pipes
# that you follow the same job naming
# convention as others e.g. validate-casablanca
#***********************************************
#               PIPELINE VARIABLES
#***********************************************
#
# PROJECT_NAME - Name of the project e.g. casablanca, fb1
# TARGET_PROJECT - Name of the pipeline we set in gitlab UI, e.g. kin-dev, fb1, exacalibur
# WORKING_DIR - gitlab working directoy, not including CI_PROJECT_DIR e.g. scully/scully-cas
# PARALLELISM - "[INT]", determine how many nodes at any apply stage gets created at one time.
# DOMAIN_TARGET - Target domain for node to get added to, DICE or Scully currently exists.
# VC_PASS - Vcenter password to be used for VSphere, when run write_ansible_files.py  and attache_vmdk.py
# VC_USER - VCenter user admin to be used for VSphere, when run write_ansible_files.py and attache_vmdk.py
# LOCAL_USERNAME - Local monkey project username, when run run_sync.ps1
# LOCAL_PASSWORD - Local monkey project passsowrd, when run run_sync.ps1
# KINIT_USER - used by ansible for kerberos winrm connection
# ANSIBLE_MAIN_MODULE - ansible main module to load

include:
  # UTILITIES
  #- local: /yml/pwsh.yml # re-enable this in the future (COBRA-5184)
  - local: /yml/terraform.yaml
  - local: /yml/templates.yml
  - local: /yml/docs.yml

  - local: /project_baselines/.gitlab-ci.yml
  - local: /projects/kin-test-ps/.gitlab-ci.yml
  - local: /projects/fb1_test_eala/.gitlab-ci.yml

  - local: /projects/gitlab_runners/dice_vmdk_runners/.gitlab-ci.yml
  - local: /projects/gitlab_runners/criterion_vmdk_runners/.gitlab-ci.yml
  - local: /projects/gitlab_runners/eala_vmdk_runners/.gitlab-ci.yml
  - local: /projects/gitlab_runners/criterion_terra_runners/.gitlab-ci.yml
  - local: /projects/dice-joss/.gitlab-ci.yml
  # DICE PowerStore (PS)
  - local: /projects/bct_ps/.gitlab-ci.yml
  - local: /projects/bct_ps_autotest/.gitlab-ci.yml
  - local: /projects/bct_ps_preflights/.gitlab-ci.yml
  - local: /projects/kingston_ps/.gitlab-ci.yml
  - local: /projects/*********************/.gitlab-ci.yml
  - local: /projects/kingston_ps_autotest/.gitlab-ci.yml
  - local: /projects/kin_autotest_ps/.gitlab-ci.yml
  - local: /projects/kingston_rel_ps/.gitlab-ci.yml
  - local: /projects/fb1_ps/.gitlab-ci.yml
  - local: /projects/bct_ado/.gitlab-ci.yml
  - local: /projects/bct_ado_ea_battlefield/.gitlab-ci.yml
  # DICE PowerStore (PS02)
  - local: /projects/bct_ps02/.gitlab-ci.yml
  - local: /projects/bct_ch1_dev_ps02/.gitlab-ci.yml
  - local: /projects/bct_ch1_autotest_ps02/.gitlab-ci.yml
  - local: /projects/bct_ch1_rel_ps02/.gitlab-ci.yml
  - local: /projects/**********************/.gitlab-ci.yml
  # DICE PowerStore (PS03)
  - local: /projects/bct_ch1_autotest_ps03/.gitlab-ci.yml
  # DICE-BUILD-INFRA-PS02
  - local: /projects/snowcache_troy/.gitlab-ci.yml
  # CRITERION
  - local: /projects/snowcache_troy_criterion/.gitlab-ci.yml
  - local: /projects/bct_criterion/.gitlab-ci.yml
  - local: /projects/bct_ch1_criterion/.gitlab-ci.yml
  - local: /projects/bct_ch1_autotest_criterion/.gitlab-ci.yml
  - local: /projects/fb1_criterion/.gitlab-ci.yml
  # EALA
  - local: /projects/bct_autotest_eala/.gitlab-ci.yml
  - local: /projects/bct_coverity/.gitlab-ci.yml
  - local: /projects/bct_ch1_autotest_eala/.gitlab-ci.yml
  - local: /projects/bct_ch1_coverity/.gitlab-ci.yml
  - local: /projects/bct_eala/.gitlab-ci.yml
  - local: /projects/bct_ch1_eala/.gitlab-ci.yml
  - local: projects/bct_ch1_rel_eala/.gitlab-ci.yml
  - local: /projects/fb1_eala/.gitlab-ci.yml
  - local: /projects/snowcache_troy_eala/.gitlab-ci.yml
