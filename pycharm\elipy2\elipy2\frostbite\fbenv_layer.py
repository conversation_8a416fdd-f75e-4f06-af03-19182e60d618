"""
fbenv_layer.py

Module for ELIPY's interactions with FbEnv.
For some functions we have different ways to interact with FbEnv, depending on Frostbite version.
Each script then only has to call the function defined here, without checking Frostbite version to
determine which version of the function to call.
"""
from __future__ import absolute_import
import os
from typing import List, Optional
import fbenv
from fbenv import _nant as nant
from elipy2 import core, frostbite_core, LOGGER
from elipy2.exceptions import ELIPYException, FbEnvCallException
from elipy2.frostbite import fbcli
from elipy2.retry_utils import elipy_retry
from elipy2.telemetry import collect_metrics


def use_fbcli():
    """
    Helper function to determine fbcli usage
    :return: true, if script should invoke fbcli
    """
    return os.environ.get("use_fbcli", "false").rstrip().lower() == "true"


def initialize():
    """
    Wrapper function to run fbenv.initialize
    :return:
    """
    fbenv.initialize()


@collect_metrics()
def get_platform_data(platform: str):
    """
    Get platform data from FbEnv.
    """
    try:
        return get_fbenv_platform_module().get_platform_data(platform=platform)
    except Exception as exc:
        raise FbEnvCallException(exc)


@collect_metrics()
def normalize_platform(platform: str):
    """
    Get normalized platform name from FbEnv.
    """
    try:
        return get_fbenv_platform_module().normalize_platform(platform=platform)
    except Exception as exc:
        raise FbEnvCallException(exc)


@collect_metrics()
def get_game_binary(platform: str, local: bool, config: str):
    """
    Get absolute path to executable for a platform-config combination.
    """
    try:
        if frostbite_core.minimum_fb_version(year=2022, season=1, version_nr="Beta"):
            game_binary_path = fbenv.get_game_binary(platform=platform, local=local, config=config)
        else:
            game_binary_path = fbenv._process.get_game_binary(  # pylint: disable=protected-access
                platform=platform, local=local, config=config
            )
        return game_binary_path
    except Exception as exc:
        raise FbEnvCallException(exc)


@collect_metrics()
def get_game_directory(platform: str, local: bool, config: str):
    """
    Get path to the local binary directory.
    """
    try:
        if frostbite_core.minimum_fb_version(year=2022, season=1, version_nr="Beta"):
            game_directory = fbenv.get_game_directory(platform=platform, local=local, config=config)
        else:
            game_directory = fbenv._process.get_game_directory(  # pylint: disable=protected-access
                platform=platform, local=local, config=config
            )
        return game_directory
    except Exception as exc:
        raise FbEnvCallException(exc)


@collect_metrics()
def get_frosted_directory(config: str):
    """
    Get path to the local binary directory for frosted.
    """
    try:
        if frostbite_core.minimum_fb_version(year=2022, season=1, version_nr="Beta"):
            frosted_directory = fbenv.get_frosted_directory(config=config)
        else:
            frosted_directory = (
                fbenv._process.get_frosted_directory(  # pylint: disable=protected-access
                    config=config
                )
            )
        return frosted_directory
    except Exception as exc:
        raise FbEnvCallException(exc)


@collect_metrics()
def get_pipeline_directory(config: str):
    """
    Get path to the local binary directory for pipeline.
    """
    try:
        if frostbite_core.minimum_fb_version(year=2022, season=1, version_nr="Beta"):
            pipeline_directory = fbenv.get_pipeline_directory(config=config)
        else:
            pipeline_directory = (
                fbenv._process.get_pipeline_directory(  # pylint: disable=protected-access
                    config=config
                )
            )
        return pipeline_directory
    except Exception as exc:
        raise FbEnvCallException(exc)


@elipy_retry(FbEnvCallException, tries=3, delay=10, backoff=2, logger=LOGGER)
@collect_metrics()
def pkgprebuilds(platform, input_param_file: str, framework_args: List[str] = []):
    """
    Create prebuilt packages for outsourcing workflow.
    Before running this, you need to build the platforms that should be included.
    """
    LOGGER.info("Creating prebuilt packages for platform {}.".format(platform))

    platforms = []
    if isinstance(platform, str):
        platforms.append(platform)
    else:
        platforms = list(platform)

    exit_code = fbenv.pkgprebuilds(
        platforms=platforms,
        input_param_file=input_param_file,
        framework_args=framework_args,
    )
    if exit_code != 0:
        raise FbEnvCallException(
            "Failed to create prebuilt packages for {}, exit code: {}".format(platforms, exit_code)
        )

    return exit_code


@collect_metrics()
def cook(
    platforms: List[str] = [],
    assets: List[str] = [],
    pipeline_args: List[str] = [],
    attach: bool = False,
    is_local: bool = True,
    index: bool = False,
):
    """
    Run the pipeline to cook (build) data.
    """
    if not platforms and not assets and not pipeline_args:
        raise ELIPYException(
            "At least one of 'platforms', 'assets' and 'pipeline_args' have to be specified."
        )
    if assets and not platforms:
        raise ELIPYException("If specifying an asset, you need to specify a platform.")
    try:
        if use_fbcli():
            fbcli.cook(
                platforms=platforms,
                assets=assets,
                pipeline_args=pipeline_args,
                attach=attach,
                is_local=is_local,
            )
        else:
            LOGGER.info("Running fbenv.cook")
            if frostbite_core.minimum_fb_version(year=2023, season=2, version_nr=1):
                cook_platforms = [] if index else platforms
                if index:
                    LOGGER.info("Indexing...")
                fbenv.cook(
                    platforms=cook_platforms,
                    assets=assets,
                    pipeline_args=pipeline_args,
                    attach=attach,
                    is_local=is_local,
                    index=index,
                )
            else:
                fbenv.cook(
                    platforms=platforms,
                    assets=assets,
                    pipeline_args=pipeline_args,
                    attach=attach,
                    is_local=is_local,
                )

    except Exception as exc:
        raise FbEnvCallException(exc)


def icepick_cook(
    platform=None,
    configuration=None,
    test_suites=None,
    args=[],
    is_local=False,
    extra_framework_args=[],
    settings_files_list=[],
):
    """
    Run Icepick cook
    """
    if use_fbcli():
        fbcli.icepick_cook(
            platform=platform,
            configuration=configuration,
            suites=test_suites,
            args=args,
            is_local=is_local,
            extra_framework_args=extra_framework_args,
        )
    else:
        try:
            fbenv.icepick_cook(
                platform=platform,
                configuration=configuration,
                suites=test_suites,
                args=args,
                is_local=is_local,
                extra_framework_args=extra_framework_args,
                settings_files=settings_files_list,
            )
        except (TypeError, fbenv.exception.DecoratorTypeError):
            fbenv.icepick_cook(
                platform=platform,
                configuration=configuration,
                suites=test_suites,
                args=args,
                is_local=is_local,
                extra_framework_args=extra_framework_args,
            )


@collect_metrics()
def frosty(platform="", format="", config="", frosty_args=[], run_bespoke=False):
    """
    Run the frosty packaging.
    """
    if not run_bespoke:
        LOGGER.info("Running fbenv.frosty")
        try:
            fbenv.frosty(platform=platform, format=format, config=config, frosty_args=frosty_args)
        except Exception as exc:
            raise FbEnvCallException(exc)
    else:
        bespoke_frosty(platform=platform, format=format, config=config, frosty_args=frosty_args)


def bespoke_frosty(platform="", format="", config="", frosty_args=[]):
    """
    Run bespoke frosty packaging.
    """
    region = ""
    region_arg = next((arg for arg in frosty_args if arg.startswith("REGION")), None)
    if region_arg is not None:
        region = region_arg.split("=")[1]
        frosty_args.remove(region_arg)

    args_to_remove = ["XBSX_PUT_BUILD_LABEL_INTO_VERSION", "IS_WIN64"]
    for arg_to_delete in args_to_remove:
        to_delete = next((arg for arg in frosty_args if arg.startswith(arg_to_delete)), None)
        if to_delete is not None:
            frosty_args.remove(to_delete)
    method_args = [platform]
    if region:
        method_args.append(region)
    method_args = method_args + [f"-{format}", f"-{config}", "--"] + frosty_args
    fbcli.run("frosty", method_args=method_args)


@collect_metrics()
def utility_path_exists():
    """
    Helper function to check if the file utility.py exists in FbEnv.
    """
    utility_path = os.path.join(
        frostbite_core.get_tnt_root(), "bin", "fbenv", "fbenv", "utility.py"
    )
    return os.path.exists(utility_path)


@collect_metrics()
def get_enabled_licensees():
    """
    Helper function to return currently enabled code licensees
    """
    if frostbite_core.minimum_fb_version(year=2022, season=1, version_nr=2):
        ret = fbenv.get_enabled_licensee_names()
    else:
        ret = nant.get_enabled_licensee_names()
    return ret


@collect_metrics()
def get_fbenv_platform_module():
    """
    Helper function to return the module in FbEnv where platform functions are located.
    """
    try:
        if (
            frostbite_core.minimum_fb_version(year=2022, season=2, version_nr="3")
            and utility_path_exists()
        ):
            platform_module = fbenv.utility
        elif frostbite_core.minimum_fb_version(year=2022, season=1, version_nr="Beta"):
            platform_module = fbenv
        else:
            platform_module = fbenv.fbenums
        return platform_module
    except Exception as exc:
        raise FbEnvCallException(exc)


@collect_metrics()
def get_exe_name():
    """
    Get the executable name to use for the current configuration.
    """
    if frostbite_core.minimum_fb_version(year=2022, season=1, version_nr="2"):
        try:
            exe_main_name = fbenv.get_exe_name()
        except Exception as exc:
            raise FbEnvCallException(exc)
    else:
        exe_main_name = os.environ.get("EXE_MAIN_NAME")
        if not exe_main_name:
            raise ELIPYException("Unable to find EXE_MAIN_NAME set in the environment.")
    return exe_main_name


@collect_metrics()
def pullfrostbitebuild(
    artifact: str,
    platforms: List[str],
    variant: str,
    remote_dir: str,
    copy_build_args: List[str],
    mirror: bool = False,
    use_local: bool = True,
    label: str = None,
    ignore_lock: bool = True,
):
    """
    Copy a build from a network share to a build machine.
    """
    _mirror = False
    if mirror:
        _mirror = frostbite_core.minimum_fb_version(year=2023, season=1, version_nr=3)
    try:
        if _mirror:
            if use_fbcli():
                fbcli.pullbuild(
                    label,
                    artifact,
                    platforms,
                    config=variant,
                    path=remote_dir,
                    local=use_local,
                    ignore_lock=ignore_lock,
                    mirror=mirror,
                    eacopy_args=copy_build_args,
                )
            else:
                fbenv.pullfrostbitebuild(
                    artifact=artifact,
                    platforms=platforms,
                    variant=variant,
                    remote_dir=remote_dir,
                    copy_build_args=copy_build_args,
                    mirror=_mirror,
                    use_local=use_local,
                )
        else:
            if use_fbcli():
                fbcli.pullbuild(
                    label,
                    artifact,
                    platforms,
                    config=variant,
                    path=remote_dir,
                    local=use_local,
                    ignore_lock=ignore_lock,
                    eacopy_args=copy_build_args,
                )
            else:
                fbenv.pullfrostbitebuild(
                    artifact=artifact,
                    platforms=platforms,
                    variant=variant,
                    remote_dir=remote_dir,
                    copy_build_args=copy_build_args,
                    use_local=use_local,
                )
    except Exception as exc:
        core.close_file_handles(os.path.join(frostbite_core.get_tnt_root(), "Local"))
        raise FbEnvCallException(exc)


@collect_metrics()
def pushfrostbitebuild(
    artifact: str,
    platforms: List[str],
    variant: str,
    remote_dir: str,
    copy_build_args: List[str],
    mirror: bool = False,
    use_local: bool = True,
    label: str = None,
):
    """
    Copy a build from a build machine to a network share.
    """
    _mirror = False
    if mirror:
        _mirror = frostbite_core.minimum_fb_version(year=2023, season=1, version_nr=3)
    try:
        if _mirror:
            if use_fbcli():
                fbcli.pushbuild(
                    label,
                    artifact,
                    platforms,
                    config=variant,
                    path=remote_dir,
                    eacopy_args=copy_build_args,
                    mirror=_mirror,
                )
            else:
                fbenv.pushfrostbitebuild(
                    artifact=artifact,
                    platforms=platforms,
                    variant=variant,
                    remote_dir=remote_dir,
                    copy_build_args=copy_build_args,
                    mirror=_mirror,
                    use_local=use_local,
                )
        else:
            if use_fbcli():
                fbcli.pushbuild(
                    label,
                    artifact,
                    platforms,
                    config=variant,
                    path=remote_dir,
                    eacopy_args=copy_build_args,
                )
            else:
                fbenv.pushfrostbitebuild(
                    artifact=artifact,
                    platforms=platforms,
                    variant=variant,
                    remote_dir=remote_dir,
                    copy_build_args=copy_build_args,
                    use_local=use_local,
                )
    except Exception as exc:
        raise FbEnvCallException(exc)


@collect_metrics()
def gensln(
    target: str,
    framework_args: List[str],
    alltests: bool,
    nomaster: bool,
    wsl: bool,
    stressbulkbuild: Optional[bool] = False,
    variants: Optional[List[str]] = [],
):
    """
    Call fbenv/fbcli to generate solution
    :param target: Platform or Tools to generate solution for.
    :param framework_args: Arguments passed directly to Framework.
    :param alltests: Flag to enable test generating.
    :param nomaster: Flag to disable master files generating.
    :param wsl: Use WSL, Windows Subsystem for Linux.
    :param variants: Config(s) to generate solution for
    :return:
    """
    kwargs = dict(
        target=target,
        framework_args=framework_args,
        nomaster=nomaster,
    )
    if stressbulkbuild:
        kwargs["stressbulkbuild"] = stressbulkbuild

    if variants:
        kwargs["variants"] = variants

    if frostbite_core.minimum_fb_version(year=2024, season=1, version_nr="Alpha") or use_fbcli():
        kwargs["alltests"] = alltests
        kwargs["wsl"] = wsl

    if use_fbcli():
        fbcli.gensln(**kwargs)
    else:
        fbenv.gensln(**kwargs)


@collect_metrics()
def buildsln(
    target: str,
    config: str,
    msbuild_args: List[str],
    fail_on_first_error: Optional[bool] = None,
    framework_args: Optional[List[str]] = None,
):
    """
    Call fbenv/fbcli to build solution
    :param target: Platform or Tools to build solution for.
    :param config: Config(s) to build solution for
    :param msbuild_args: Build directly with msbuild, skipping nant.
    :param fail_on_first_error: If True, msbuild will stop the build on the first error that occurs.
    :param framework_args: Arguments passed directly to Framework.
    :return:
    """
    kwargs = dict(target=target, config=config, msbuild_args=msbuild_args)
    if fail_on_first_error is not None:
        kwargs["fail_on_first_error"] = fail_on_first_error

    if use_fbcli():
        fbcli.buildsln(**kwargs)
    else:
        if framework_args is not None:
            kwargs["framework_args"] = framework_args
        fbenv.buildsln(**kwargs)


def set_datadir(
    path: str,
    noupdatelicensee: bool = False,
    noupdateshell: bool = False,
    silent: bool = False,
    switchlicensee: bool = False,
    temporary: bool = False,
):
    """
    Call fbenv/fbcli to set data directory
    :param path: Path to a valid dataset.
    :param noupdatelicensee: Don't update the licensee along with the datadir.
    :param noupdateshell: Don't update the fbcli shell variables.
    :param silent: Don't print anything to output.
    :param switchlicensee: Switch to new licensee if appropriate. (override noupdatelicensee option)
    :param temporary: Only change settings for this fbcli session.
    :return:
    """

    if use_fbcli():
        fbcli.set_datadir(
            path,
            noupdatelicensee=noupdatelicensee,
            noupdateshell=noupdateshell,
            silent=silent,
            switchlicensee=switchlicensee,
            temporary=temporary,
        )
        fbenv.initialize()
    else:
        fbenv.set_datadir(path, store_value=True)


def set_local_root(local_root: str) -> None:
    """
    Set the local root on the build machine.

    :param local_root: The path to the local root
    :return: None
    """
    try:
        fbenv.set_local_root(local_root=local_root)
    except Exception as exc:
        raise FbEnvCallException(exc)


def is_api_function_failed_exception(exc):
    """
    Check function to determine if exception object is ApiFunctionFailedException type
    :param exc: exception object to check
    :return: true, if exc is ApiFunctionFailedException type
    """
    return isinstance(exc, fbenv.exception.ApiFunctionFailedException)


def set_environment_values(values: dict):
    """
    Set environment values in fbenv

    :param values: A dictionary of environment values to set
    """
    try:
        fbenv.update_environment_values(values)
    except Exception as exc:
        raise FbEnvCallException(exc)


@collect_metrics()
def install_prerequisites(buildmachine: bool = True, noavalanche: bool = True):
    """
    Install Frostbite prerequisites including CommunicationHub

    :param buildmachine: Add -buildmachine flag to the command
    :param noavalanche: Add -noavalanche flag to the command
    :return: None
    """

    # There is no fbenv function for installing prerequisites
    LOGGER.info("Installing Frostbite prerequisites...")
    fbcli.install_prerequisites(buildmachine=buildmachine, noavalanche=noavalanche)
