"""
test_fbenv_layer.py
"""
import os
import pytest
from mock import call, MagicMock, patch
from elipy2.exceptions import ELIPYException, FbEnvCallException
from elipy2.frostbite import fbenv_layer


@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("elipy2.frostbite.fbenv_layer.os.path.join", MagicMock())
class TestFbenvLayer(object):
    def setup(self):
        # Mock minimum_fb_version
        self.patcher_minimum_fb_version = patch("elipy2.frostbite_core.minimum_fb_version")
        self.mock_minimum_fb_version = self.patcher_minimum_fb_version.start()
        self.mock_minimum_fb_version.return_value = False

        # Mock get_platform_data, three versions: internal, utility and API
        self.patcher_get_plt_data_int = patch(
            "elipy2.frostbite.fbenv_layer.fbenv.fbenums.get_platform_data"
        )
        self.mock_get_plt_data_int = self.patcher_get_plt_data_int.start()

        self.patcher_get_plt_data_uti = patch(
            "elipy2.frostbite.fbenv_layer.fbenv.utility.get_platform_data"
        )
        self.mock_get_plt_data_uti = self.patcher_get_plt_data_uti.start()

        self.patcher_get_plt_data_api = patch(
            "elipy2.frostbite.fbenv_layer.fbenv.get_platform_data"
        )
        self.mock_get_plt_data_api = self.patcher_get_plt_data_api.start()

        # Mock normalize_platform, three versions: internal, utility and API
        self.patcher_norm_plt_int = patch(
            "elipy2.frostbite.fbenv_layer.fbenv.fbenums.normalize_platform"
        )
        self.mock_norm_plt_int = self.patcher_norm_plt_int.start()

        self.patcher_norm_plt_uti = patch(
            "elipy2.frostbite.fbenv_layer.fbenv.utility.normalize_platform"
        )
        self.mock_norm_plt_uti = self.patcher_norm_plt_uti.start()

        self.patcher_norm_plt_api = patch("elipy2.frostbite.fbenv_layer.fbenv.normalize_platform")
        self.mock_norm_plt_api = self.patcher_norm_plt_api.start()

        # Mock get_game_binary, internal version and API version
        self.patcher_game_bin_int = patch(
            "elipy2.frostbite.fbenv_layer.fbenv._process.get_game_binary"
        )
        self.mock_game_bin_int = self.patcher_game_bin_int.start()

        self.patcher_game_bin_api = patch("elipy2.frostbite.fbenv_layer.fbenv.get_game_binary")
        self.mock_game_bin_api = self.patcher_game_bin_api.start()

        # Mock get_game_directory, internal version and API version
        self.patcher_game_dir_int = patch(
            "elipy2.frostbite.fbenv_layer.fbenv._process.get_game_directory"
        )
        self.mock_game_dir_int = self.patcher_game_dir_int.start()

        self.patcher_game_dir_api = patch("elipy2.frostbite.fbenv_layer.fbenv.get_game_directory")
        self.mock_game_dir_api = self.patcher_game_dir_api.start()

        # Mock get_frosted_directory, internal version and API version
        self.patcher_frosted_dir_int = patch(
            "elipy2.frostbite.fbenv_layer.fbenv._process.get_frosted_directory"
        )
        self.mock_frosted_dir_int = self.patcher_frosted_dir_int.start()

        self.patcher_frosted_dir_api = patch(
            "elipy2.frostbite.fbenv_layer.fbenv.get_frosted_directory"
        )
        self.mock_frosted_dir_api = self.patcher_frosted_dir_api.start()

        # Mock get_pipeline_directory, internal version and API version
        self.patcher_pipeline_dir_int = patch(
            "elipy2.frostbite.fbenv_layer.fbenv._process.get_pipeline_directory"
        )
        self.mock_pipeline_dir_int = self.patcher_pipeline_dir_int.start()

        self.patcher_pipeline_dir_api = patch(
            "elipy2.frostbite.fbenv_layer.fbenv.get_pipeline_directory"
        )
        self.mock_pipeline_dir_api = self.patcher_pipeline_dir_api.start()

        # Mock pkgprebuilds
        self.patcher_pkgprebuilds = patch("elipy2.frostbite.fbenv_layer.fbenv.pkgprebuilds")
        self.mock_pkgprebuilds = self.patcher_pkgprebuilds.start()

        # Mock cook
        self.patcher_cook = patch("elipy2.frostbite.fbenv_layer.fbenv.cook")
        self.mock_cook = self.patcher_cook.start()

        self.patcher_cook_fbcli = patch("elipy2.frostbite.fbenv_layer.fbcli.cook")
        self.mock_cook_fbcli = self.patcher_cook_fbcli.start()

        # Mock get_exe_name
        self.patcher_get_exe_name = patch("elipy2.frostbite.fbenv_layer.fbenv.get_exe_name")
        self.mock_get_exe_name = self.patcher_get_exe_name.start()

        # Mock pullfrostbitebuild
        self.patcher_pullfrostbitebuild = patch(
            "elipy2.frostbite.fbenv_layer.fbenv.pullfrostbitebuild"
        )
        self.mock_pullfrostbitebuild = self.patcher_pullfrostbitebuild.start()

        # Mock pushfrostbitebuild
        self.patcher_pushfrostbitebuild = patch(
            "elipy2.frostbite.fbenv_layer.fbenv.pushfrostbitebuild"
        )
        self.mock_pushfrostbitebuild = self.patcher_pushfrostbitebuild.start()

        # Mock fbcli pullbuild
        self.patcher_fbcli_pullbuild = patch("elipy2.frostbite.fbcli.pullbuild")
        self.mock_fbcli_pullbuild = self.patcher_fbcli_pullbuild.start()

        # Mock fbcli pushbuild
        self.patcher_fbcli_pushbuild = patch("elipy2.frostbite.fbcli.pushbuild")
        self.mock_fbcli_pushbuild = self.patcher_fbcli_pushbuild.start()

        # Mock frosty
        self.patcher_frosty = patch("elipy2.frostbite.fbenv_layer.fbenv.frosty")
        self.mock_frosty = self.patcher_frosty.start()

        # Mock set_datadir
        self.patcher_set_datadir = patch("elipy2.frostbite.fbenv_layer.fbenv.set_datadir")
        self.mock_set_datadir = self.patcher_set_datadir.start()

        # Mock buildsln
        self.patcher_buildsln = patch("elipy2.frostbite.fbenv_layer.fbenv.buildsln")
        self.mock_buildsln = self.patcher_buildsln.start()

        # Mock gensln
        self.patcher_gensln = patch("elipy2.frostbite.fbenv_layer.fbenv.gensln")
        self.mock_gensln = self.patcher_gensln.start()

        # Mock old (nant) get_enabled_licensee_names
        self.patcher_nant_licensees = patch(
            "elipy2.frostbite.fbenv_layer.fbenv._nant.get_enabled_licensee_names"
        )
        self.mock_nant_licensees = self.patcher_nant_licensees.start()

        # Mock new (fbenv) get_enabled_licensee_names
        self.patcher_fbenv_licensees = patch(
            "elipy2.frostbite.fbenv_layer.fbenv.get_enabled_licensee_names"
        )
        self.mock_fbenv_licensees = self.patcher_fbenv_licensees.start()

        # Mock set_local_root
        self.patcher_set_local_root = patch("elipy2.frostbite.fbenv_layer.fbenv.set_local_root")
        self.mock_set_local_root = self.patcher_set_local_root.start()

    def teardown(self):
        patch.stopall()

    @patch("elipy2.frostbite.fbenv_layer.get_fbenv_platform_module")
    def test_get_platform_data(self, mock_fbenv_platform_module):
        mock_fbenv_platform_module.return_value = MagicMock()
        mock_fbenv_platform_module.return_value.get_platform_data.return_value = {
            "some_key": "some_value"
        }
        assert fbenv_layer.get_platform_data(platform="win64") == {"some_key": "some_value"}
        assert mock_fbenv_platform_module.call_count == 1

    @patch("elipy2.frostbite.fbenv_layer.get_fbenv_platform_module")
    def test_get_platform_data_exception(self, mock_fbenv_platform_module):
        mock_fbenv_platform_module.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.get_platform_data(platform="win64")

    @patch("elipy2.frostbite.fbenv_layer.get_fbenv_platform_module")
    def test_normalize_platform(self, mock_fbenv_platform_module):
        mock_fbenv_platform_module.return_value = MagicMock()
        mock_fbenv_platform_module.return_value.normalize_platform.return_value = "win64"
        assert fbenv_layer.normalize_platform(platform="win64") == "win64"
        assert mock_fbenv_platform_module.call_count == 1

    @patch("elipy2.frostbite.fbenv_layer.get_fbenv_platform_module")
    def test_normalize_platform_exception(self, mock_fbenv_platform_module):
        mock_fbenv_platform_module.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.normalize_platform(platform="win64")

    def test_get_game_binary_internal(self):
        self.mock_minimum_fb_version.return_value = False
        self.mock_game_bin_int.return_value = "some\\path\\binary.exe"
        assert (
            fbenv_layer.get_game_binary(platform="win64", local=True, config="final")
            == "some\\path\\binary.exe"
        )
        self.mock_game_bin_int.assert_called_once_with(platform="win64", local=True, config="final")
        assert not self.mock_game_bin_api.called

    def test_get_game_binary_api(self):
        self.mock_minimum_fb_version.return_value = True
        self.mock_game_bin_api.return_value = "some\\path\\binary.exe"
        assert (
            fbenv_layer.get_game_binary(platform="win64", local=True, config="final")
            == "some\\path\\binary.exe"
        )
        self.mock_game_bin_api.assert_called_once_with(platform="win64", local=True, config="final")
        assert not self.mock_game_bin_int.called

    def test_get_game_binary_exception(self):
        self.mock_minimum_fb_version.return_value = True
        self.mock_game_bin_api.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.get_game_binary(platform="win64", local=True, config="final")

    def test_get_game_directory_internal(self):
        self.mock_minimum_fb_version.return_value = False
        self.mock_game_dir_int.return_value = "some\\path"
        assert (
            fbenv_layer.get_game_directory(platform="win64", local=True, config="final")
            == "some\\path"
        )
        self.mock_game_dir_int.assert_called_once_with(platform="win64", local=True, config="final")
        assert not self.mock_game_dir_api.called

    def test_get_game_directory_api(self):
        self.mock_minimum_fb_version.return_value = True
        self.mock_game_dir_api.return_value = "some\\path"
        assert (
            fbenv_layer.get_game_directory(platform="win64", local=True, config="final")
            == "some\\path"
        )
        self.mock_game_dir_api.assert_called_once_with(platform="win64", local=True, config="final")
        assert not self.mock_game_dir_int.called

    def test_get_game_directory_exception(self):
        self.mock_minimum_fb_version.return_value = True
        self.mock_game_dir_api.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.get_game_directory(platform="win64", local=True, config="final")

    def test_get_frosted_directory_internal(self):
        self.mock_minimum_fb_version.return_value = False
        self.mock_frosted_dir_int.return_value = "some\\path"
        assert fbenv_layer.get_frosted_directory(config="release") == "some\\path"
        self.mock_frosted_dir_int.assert_called_once_with(config="release")
        assert not self.mock_frosted_dir_api.called

    def test_get_frosted_directory_api(self):
        self.mock_minimum_fb_version.return_value = True
        self.mock_frosted_dir_api.return_value = "some\\path"
        assert fbenv_layer.get_frosted_directory(config="release") == "some\\path"
        self.mock_frosted_dir_api.assert_called_once_with(config="release")
        assert not self.mock_frosted_dir_int.called

    def test_get_frosted_directory_exception(self):
        self.mock_minimum_fb_version.return_value = True
        self.mock_frosted_dir_api.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.get_frosted_directory(config="release")

    def test_get_pipeline_directory_internal(self):
        self.mock_minimum_fb_version.return_value = False
        self.mock_pipeline_dir_int.return_value = "some\\path"
        assert fbenv_layer.get_pipeline_directory(config="release") == "some\\path"
        self.mock_pipeline_dir_int.assert_called_once_with(config="release")
        assert not self.mock_pipeline_dir_api.called

    def test_get_pipeline_directory_api(self):
        self.mock_minimum_fb_version.return_value = True
        self.mock_pipeline_dir_api.return_value = "some\\path"
        assert fbenv_layer.get_pipeline_directory(config="release") == "some\\path"
        self.mock_pipeline_dir_api.assert_called_once_with(config="release")
        assert not self.mock_pipeline_dir_int.called

    def test_get_pipeline_directory_exception(self):
        self.mock_minimum_fb_version.return_value = True
        self.mock_pipeline_dir_api.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.get_pipeline_directory(config="release")

    def test_pkgprebuilds(self):
        self.mock_pkgprebuilds.return_value = 0
        fbenv_layer.pkgprebuilds(platform="win64game", input_param_file="input_param_file")
        self.mock_pkgprebuilds.assert_called_once_with(
            platforms=["win64game"], input_param_file="input_param_file", framework_args=[]
        )

    def test_pkgprebuilds_framework_args(self):
        self.mock_pkgprebuilds.return_value = 0
        fbenv_layer.pkgprebuilds(
            platform="win64game",
            input_param_file="input_param_file",
            framework_args=["arg1", "arg2"],
        )
        self.mock_pkgprebuilds.assert_called_once_with(
            platforms=["win64game"],
            input_param_file="input_param_file",
            framework_args=["arg1", "arg2"],
        )

    def test_pkgprebuilds_framework_args_list_of_platforms(self):
        self.mock_pkgprebuilds.return_value = 0
        fbenv_layer.pkgprebuilds(
            platform=["win64game", "Tools"],
            input_param_file="input_param_file",
            framework_args=["arg1", "arg2"],
        )
        self.mock_pkgprebuilds.assert_called_once_with(
            platforms=["win64game", "Tools"],
            input_param_file="input_param_file",
            framework_args=["arg1", "arg2"],
        )

    def test_pkgprebuilds_framework_args_tuple_of_platforms(self):
        self.mock_pkgprebuilds.return_value = 0
        fbenv_layer.pkgprebuilds(
            platform=("win64game", "Tools"),
            input_param_file="input_param_file",
            framework_args=["arg1", "arg2"],
        )
        self.mock_pkgprebuilds.assert_called_once_with(
            platforms=["win64game", "Tools"],
            input_param_file="input_param_file",
            framework_args=["arg1", "arg2"],
        )

    def test_pkgprebuilds_failure_no_platform(self):
        self.mock_pkgprebuilds.return_value = 0
        with pytest.raises(TypeError):
            fbenv_layer.pkgprebuilds(input_param_file="input_param_file")

    def test_pkgprebuilds_failure_no_input_file(self):
        self.mock_pkgprebuilds.return_value = 0
        with pytest.raises(TypeError):
            fbenv_layer.pkgprebuilds(platform="win64game")

    def test_pkgprebuilds_failure_fbenv(self):
        self.mock_pkgprebuilds.return_value = 1
        with pytest.raises(FbEnvCallException):
            fbenv_layer.pkgprebuilds(platform="win64game", input_param_file="input_param_file")

    def test_cook_only_platforms(self):
        fbenv_layer.cook(platforms=["win64"])
        self.mock_cook.assert_called_once_with(
            platforms=["win64"], assets=[], pipeline_args=[], attach=False, is_local=True
        )

    @patch.dict(os.environ, {"use_fbcli": "True"}, clear=True)
    def test_cook_fbcli_only_platforms(self):
        fbenv_layer.cook(platforms=["win64"])
        self.mock_cook_fbcli.assert_called_once_with(
            platforms=["win64"], assets=[], pipeline_args=[], attach=False, is_local=True
        )

    def test_cook_only_pipelineargs(self):
        fbenv_layer.cook(pipeline_args=["arg1"])
        self.mock_cook.assert_called_once_with(
            platforms=[], assets=[], pipeline_args=["arg1"], attach=False, is_local=True
        )

    @patch.dict(os.environ, {"use_fbcli": "True"}, clear=True)
    def test_cook_fbcli_only_pipelineargs(self):
        fbenv_layer.cook(pipeline_args=["arg1"])
        self.mock_cook_fbcli.assert_called_once_with(
            platforms=[], assets=[], pipeline_args=["arg1"], attach=False, is_local=True
        )

    def test_cook_platforms_assets(self):
        fbenv_layer.cook(platforms=["win64"], assets=["test_asset"])
        self.mock_cook.assert_called_once_with(
            platforms=["win64"],
            assets=["test_asset"],
            pipeline_args=[],
            attach=False,
            is_local=True,
        )

    @patch.dict(os.environ, {"use_fbcli": "True"}, clear=True)
    def test_cook_fbcli_platforms_assets(self):
        fbenv_layer.cook(platforms=["win64"], assets=["test_asset"])
        self.mock_cook_fbcli.assert_called_once_with(
            platforms=["win64"],
            assets=["test_asset"],
            pipeline_args=[],
            attach=False,
            is_local=True,
        )

    def test_cook_platforms_pipelineargs(self):
        fbenv_layer.cook(platforms=["win64"], pipeline_args=["arg1"])
        self.mock_cook.assert_called_once_with(
            platforms=["win64"], assets=[], pipeline_args=["arg1"], attach=False, is_local=True
        )

    @patch.dict(os.environ, {"use_fbcli": "True"}, clear=True)
    def test_cook_fbcli_platforms_pipelineargs(self):
        fbenv_layer.cook(platforms=["win64"], pipeline_args=["arg1"])
        self.mock_cook_fbcli.assert_called_once_with(
            platforms=["win64"], assets=[], pipeline_args=["arg1"], attach=False, is_local=True
        )

    def test_cook_all_params(self):
        fbenv_layer.cook(platforms=["win64"], assets=["test_asset"], pipeline_args=["arg1"])
        self.mock_cook.assert_called_once_with(
            platforms=["win64"],
            assets=["test_asset"],
            pipeline_args=["arg1"],
            attach=False,
            is_local=True,
        )

    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=True))
    def test_cook_all_params_new_fb(self):
        fbenv_layer.cook(platforms=["win64"], assets=["test_asset"], pipeline_args=["arg1"])
        self.mock_cook.assert_called_once_with(
            platforms=["win64"],
            assets=["test_asset"],
            pipeline_args=["arg1"],
            attach=False,
            is_local=True,
            index=False,
        )

    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=True))
    def test_cook_index(self):
        fbenv_layer.cook(index=True, platforms=["ignored"], assets=[], pipeline_args=[])
        self.mock_cook.assert_called_once_with(
            platforms=[],
            assets=[],
            pipeline_args=[],
            attach=False,
            is_local=True,
            index=True,
        )

    def test_cook_index_deprecated(self):
        fbenv_layer.cook(platforms=["index"])
        self.mock_cook.assert_called_once_with(
            platforms=["index"],
            assets=[],
            pipeline_args=[],
            attach=False,
            is_local=True,
        )

    @patch.dict(os.environ, {"use_fbcli": "True"}, clear=True)
    def test_cook_fbcli_all_params(self):
        fbenv_layer.cook(platforms=["win64"], assets=["test_asset"], pipeline_args=["arg1"])
        self.mock_cook_fbcli.assert_called_once_with(
            platforms=["win64"],
            assets=["test_asset"],
            pipeline_args=["arg1"],
            attach=False,
            is_local=True,
        )

    def test_cook_not_local(self):
        fbenv_layer.cook(platforms=["win64"], is_local=False)
        self.mock_cook.assert_called_once_with(
            platforms=["win64"], assets=[], pipeline_args=[], attach=False, is_local=False
        )

    @patch.dict(os.environ, {"use_fbcli": "True"}, clear=True)
    def test_cook_fbcli_not_local(self):
        fbenv_layer.cook(platforms=["win64"], is_local=False)
        self.mock_cook_fbcli.assert_called_once_with(
            platforms=["win64"], assets=[], pipeline_args=[], attach=False, is_local=False
        )

    def test_cook_attach(self):
        fbenv_layer.cook(platforms=["win64"], attach=True)
        self.mock_cook.assert_called_once_with(
            platforms=["win64"], assets=[], pipeline_args=[], attach=True, is_local=True
        )

    @patch.dict(os.environ, {"use_fbcli": "True"}, clear=True)
    def test_cook_fbcli_attach(self):
        fbenv_layer.cook(platforms=["win64"], attach=True)
        self.mock_cook_fbcli.assert_called_once_with(
            platforms=["win64"], assets=[], pipeline_args=[], attach=True, is_local=True
        )

    def test_cook_without_params_fail(self):
        with pytest.raises(ELIPYException):
            fbenv_layer.cook()

    def test_cook_only_assets_fail(self):
        with pytest.raises(ELIPYException):
            fbenv_layer.cook(assets=["test_asset"])

    def test_cook_assets_pipelineargs_fail(self):
        with pytest.raises(ELIPYException):
            fbenv_layer.cook(assets=["test_asset"], pipeline_args=["arg1"])

    def test_cook_fbenv_exception(self):
        self.mock_cook.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.cook(platforms=["win64"])

    @patch("elipy2.frostbite.fbenv_layer.os.path.exists")
    def test_utility_path_exists_true(self, mock_exists):
        mock_exists.return_value = True
        assert fbenv_layer.utility_path_exists() == True

    @patch("elipy2.frostbite.fbenv_layer.os.path.exists")
    def test_utility_path_exists_false(self, mock_exists):
        mock_exists.return_value = False
        assert fbenv_layer.utility_path_exists() == False

    def test_get_fbenv_platform_module_before_2022(self):
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.get_fbenv_platform_module().get_platform_data(platform="win64")
        self.mock_get_plt_data_int.assert_called_once_with(platform="win64")

    def test_get_fbenv_platform_module_early_2022(self):
        self.mock_minimum_fb_version.side_effect = [False, True]
        fbenv_layer.get_fbenv_platform_module().get_platform_data(platform="win64")
        self.mock_get_plt_data_api.assert_called_once_with(platform="win64")

    @patch("elipy2.frostbite.fbenv_layer.utility_path_exists")
    def test_get_fbenv_platform_module_late_2022_no_utility_file(self, mock_utility_path_exists):
        self.mock_minimum_fb_version.return_value = True
        mock_utility_path_exists.return_value = False
        fbenv_layer.get_fbenv_platform_module().get_platform_data(platform="win64")
        self.mock_get_plt_data_api.assert_called_once_with(platform="win64")

    @patch("elipy2.frostbite.fbenv_layer.utility_path_exists")
    def test_get_fbenv_platform_module_late_2022_with_utility_file(self, mock_utility_path_exists):
        self.mock_minimum_fb_version.return_value = True
        mock_utility_path_exists.return_value = True
        fbenv_layer.get_fbenv_platform_module().get_platform_data(platform="win64")
        self.mock_get_plt_data_uti.assert_called_once_with(platform="win64")

    @patch("elipy2.frostbite.fbenv_layer.fbenv")
    @patch("elipy2.frostbite.fbenv_layer.utility_path_exists")
    def test_get_fbenv_platform_module_late_2022_no_utility_file_exception(
        self, mock_fbenv, mock_utility_path_exists
    ):
        self.mock_minimum_fb_version.return_value = True
        mock_utility_path_exists.return_value = False
        mock_fbenv.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.get_fbenv_platform_module()

    @patch("elipy2.frostbite.fbenv_layer.fbenv.utility")
    @patch("elipy2.frostbite.fbenv_layer.utility_path_exists")
    def test_get_fbenv_platform_module_late_2022_with_utility_file_exception(
        self, mock_utility, mock_utility_path_exists
    ):
        self.mock_minimum_fb_version.return_value = True
        mock_utility_path_exists.return_value = True
        mock_utility.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.get_fbenv_platform_module()

    def test_get_exe_name_2022(self):
        self.mock_minimum_fb_version.return_value = True
        self.mock_get_exe_name.return_value = "exe_name"
        assert fbenv_layer.get_exe_name() == "exe_name"

    @patch("os.environ.get")
    def test_get_exe_name_pre_2022(self, mock_os_environ_get):
        self.mock_minimum_fb_version.return_value = False
        mock_os_environ_get.return_value = "exe_name"
        assert fbenv_layer.get_exe_name() == "exe_name"

    def test_get_exe_name_2022_failure(self):
        self.mock_minimum_fb_version.return_value = True
        self.mock_get_exe_name.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.get_exe_name()

    @patch("os.environ.get")
    def test_get_exe_name_pre_2022_failure(self, mock_os_environ_get):
        self.mock_minimum_fb_version.return_value = False
        mock_os_environ_get.return_value = None
        with pytest.raises(ELIPYException):
            fbenv_layer.get_exe_name()

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_pullfrostbitebuild_use_fbcli_default(self, mock_use_fbcli):
        self.mock_minimum_fb_version.return_value = False
        mock_use_fbcli.return_value = True
        fbenv_layer.pullfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main",
            label="1234",
            copy_build_args=[],
        )
        self.mock_pullfrostbitebuild.assert_not_called()
        self.mock_fbcli_pullbuild.assert_called_once_with(
            "1234",
            "examplegame",
            ["win64server"],
            config="final",
            path="\\\\test_buildshare\\Code\\main",
            eacopy_args=[],
            local=True,
            ignore_lock=True,
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_pullfrostbitebuild_use_fbcli_extra_args(self, mock_use_fbcli):
        self.mock_minimum_fb_version.return_value = False
        mock_use_fbcli.return_value = True
        extra_args = ["extra", "args"]
        fbenv_layer.pullfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main",
            label="1234",
            copy_build_args=extra_args,
        )
        self.mock_pullfrostbitebuild.assert_not_called()
        self.mock_fbcli_pullbuild.assert_called_once_with(
            "1234",
            "examplegame",
            ["win64server"],
            config="final",
            path="\\\\test_buildshare\\Code\\main",
            eacopy_args=extra_args,
            local=True,
            ignore_lock=True,
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_pullfrostbitebuild_use_fbcli_not_use_local(self, mock_use_fbcli):
        self.mock_minimum_fb_version.return_value = False
        mock_use_fbcli.return_value = True
        fbenv_layer.pullfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main",
            label="1234",
            copy_build_args=[],
            use_local=False,
        )
        self.mock_pullfrostbitebuild.assert_not_called()
        self.mock_fbcli_pullbuild.assert_called_once_with(
            "1234",
            "examplegame",
            ["win64server"],
            config="final",
            path="\\\\test_buildshare\\Code\\main",
            eacopy_args=[],
            local=False,
            ignore_lock=True,
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_pullfrostbitebuild_use_fbcli_not_ignore_lock(self, mock_use_fbcli):
        self.mock_minimum_fb_version.return_value = False
        mock_use_fbcli.return_value = True
        fbenv_layer.pullfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main",
            label="1234",
            copy_build_args=[],
            ignore_lock=False,
        )
        self.mock_pullfrostbitebuild.assert_not_called()
        self.mock_fbcli_pullbuild.assert_called_once_with(
            "1234",
            "examplegame",
            ["win64server"],
            config="final",
            path="\\\\test_buildshare\\Code\\main",
            eacopy_args=[],
            local=True,
            ignore_lock=False,
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_pullfrostbitebuild_use_fbcli_mirror_skip(self, mock_use_fbcli):
        self.mock_minimum_fb_version.return_value = False
        mock_use_fbcli.return_value = True
        fbenv_layer.pullfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main",
            label="1234",
            copy_build_args=[],
            mirror=True,
        )
        self.mock_pullfrostbitebuild.assert_not_called()
        self.mock_fbcli_pullbuild.assert_called_once_with(
            "1234",
            "examplegame",
            ["win64server"],
            config="final",
            path="\\\\test_buildshare\\Code\\main",
            eacopy_args=[],
            local=True,
            ignore_lock=True,
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_pullfrostbitebuild_use_fbcli_mirror_use(self, mock_use_fbcli):
        self.mock_minimum_fb_version.return_value = True
        mock_use_fbcli.return_value = True
        fbenv_layer.pullfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main",
            label="1234",
            copy_build_args=[],
            mirror=True,
        )
        self.mock_pullfrostbitebuild.assert_not_called()
        self.mock_fbcli_pullbuild.assert_called_once_with(
            "1234",
            "examplegame",
            ["win64server"],
            config="final",
            path="\\\\test_buildshare\\Code\\main",
            eacopy_args=[],
            local=True,
            ignore_lock=True,
            mirror=True,
        )

    def test_pullfrostbitebuild_default(self):
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.pullfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
        )
        self.mock_pullfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=True,
        )

    def test_pullfrostbitebuild_use_local(self):
        self.mock_minimum_fb_version.return_value = True
        fbenv_layer.pullfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=True,
        )
        self.mock_pullfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=True,
        )

    def test_pullfrostbitebuild_not_use_local(self):
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.pullfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=False,
        )
        self.mock_pullfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=False,
        )

    @patch("elipy2.core.close_file_handles")
    def test_pullfrostbitebuild_exception(self, mock_close_file_handles):
        self.mock_pullfrostbitebuild.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.pullfrostbitebuild(
                artifact="examplegame",
                platforms=["win64server"],
                variant="final",
                remote_dir="\\\\test_buildshare\\Code\\main\\1234",
                copy_build_args=[],
                use_local=False,
            )

    def test_pullfrostbitebuild_mirror_skip(self):
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.pullfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            mirror=True,
        )
        self.mock_pullfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=True,
        )

    def test_pullfrostbitebuild_mirror_use(self):
        self.mock_minimum_fb_version.return_value = True
        fbenv_layer.pullfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            mirror=True,
        )
        self.mock_pullfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=True,
            mirror=True,
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_pushfrostbitebuild_use_fbcli_default(self, mock_use_fbcli):
        self.mock_minimum_fb_version.return_value = False
        mock_use_fbcli.return_value = True
        fbenv_layer.pushfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main",
            label="1234",
            copy_build_args=[],
        )
        self.mock_pushfrostbitebuild.assert_not_called()
        self.mock_fbcli_pushbuild.assert_called_once_with(
            "1234",
            "examplegame",
            ["win64server"],
            config="final",
            path="\\\\test_buildshare\\Code\\main",
            eacopy_args=[],
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_pushfrostbitebuild_use_fbcli_extra_args(self, mock_use_fbcli):
        self.mock_minimum_fb_version.return_value = False
        mock_use_fbcli.return_value = True
        extra_args = ["extra", "args"]
        fbenv_layer.pushfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main",
            label="1234",
            copy_build_args=extra_args,
        )
        self.mock_pushfrostbitebuild.assert_not_called()
        self.mock_fbcli_pushbuild.assert_called_once_with(
            "1234",
            "examplegame",
            ["win64server"],
            config="final",
            path="\\\\test_buildshare\\Code\\main",
            eacopy_args=extra_args,
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_pushfrostbitebuild_use_fbcli_mirror_skip(self, mock_use_fbcli):
        self.mock_minimum_fb_version.return_value = False
        mock_use_fbcli.return_value = True
        fbenv_layer.pushfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main",
            label="1234",
            copy_build_args=[],
            mirror=True,
        )
        self.mock_pushfrostbitebuild.assert_not_called()
        self.mock_fbcli_pushbuild.assert_called_once_with(
            "1234",
            "examplegame",
            ["win64server"],
            config="final",
            path="\\\\test_buildshare\\Code\\main",
            eacopy_args=[],
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_pushfrostbitebuild_use_fbcli_mirror_use(self, mock_use_fbcli):
        self.mock_minimum_fb_version.return_value = True
        mock_use_fbcli.return_value = True
        fbenv_layer.pushfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main",
            label="1234",
            copy_build_args=[],
            mirror=True,
        )
        self.mock_pushfrostbitebuild.assert_not_called()
        self.mock_fbcli_pushbuild.assert_called_once_with(
            "1234",
            "examplegame",
            ["win64server"],
            config="final",
            path="\\\\test_buildshare\\Code\\main",
            eacopy_args=[],
            mirror=True,
        )

    def test_pushfrostbitebuild_default(self):
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.pushfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
        )
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=True,
        )

    def test_pushfrostbitebuild_use_local(self):
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.pushfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=True,
        )
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=True,
        )

    def test_pushfrostbitebuild_not_use_local(self):
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.pushfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=False,
        )
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=False,
        )

    def test_pushfrostbitebuild_exception(self):
        self.mock_pushfrostbitebuild.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.pushfrostbitebuild(
                artifact="examplegame",
                platforms=["win64server"],
                variant="final",
                remote_dir="\\\\test_buildshare\\Code\\main\\1234",
                copy_build_args=[],
                use_local=False,
            )

    def test_pushfrostbitebuild_mirror_skip(self):
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.pushfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            mirror=True,
        )
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=True,
        )

    def test_pushfrostbitebuild_mirror_use(self):
        self.mock_minimum_fb_version.return_value = True
        fbenv_layer.pushfrostbitebuild(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            mirror=True,
        )
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir="\\\\test_buildshare\\Code\\main\\1234",
            copy_build_args=[],
            use_local=True,
            mirror=True,
        )

    def test_frosty_only_platforms(self):
        fbenv_layer.frosty(platform="win64")
        self.mock_frosty.assert_called_once_with(
            platform="win64", format="", config="", frosty_args=[]
        )

    def test_frosty_all_params_no_bespoke(self):
        fbenv_layer.frosty(
            platform="win64",
            format="digital",
            config="retail",
            frosty_args=["Arg1"],
        )
        self.mock_frosty.assert_called_once_with(
            platform="win64",
            format="digital",
            config="retail",
            frosty_args=["Arg1"],
        )

    def test_frosty_failure(self):
        self.mock_frosty.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.frosty(
                platform="win64",
                format="digital",
                config="retail",
                frosty_args=["Arg1"],
            )

    @patch("elipy2.frostbite.fbenv_layer.bespoke_frosty")
    def test_frosty_with_bespoke_flag(self, mock_bespoke_frosty):
        fbenv_layer.frosty(
            platform="win64",
            format="digital",
            config="retail",
            frosty_args=["Arg1"],
            run_bespoke=True,
        )
        mock_bespoke_frosty.assert_called_once_with(
            platform="win64",
            format="digital",
            config="retail",
            frosty_args=["Arg1"],
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_bespoke_frosty(self, mock_fbcli_run):
        platform = "win64"
        format = "digital"
        config = "retail"
        frosty_args = ["Arg1"]
        fbenv_layer.bespoke_frosty(
            platform=platform, format=format, config=config, frosty_args=frosty_args
        )
        expected_method_args = [platform, f"-{format}", f"-{config}", "--", "Arg1"]
        mock_fbcli_run.assert_called_once_with("frosty", method_args=expected_method_args)

    @patch("elipy2.frostbite.fbcli.run")
    def test_bespoke_frosty_region(self, mock_fbcli_run):
        platform = "win64"
        format = "digital"
        config = "retail"
        frosty_args = ["Arg1", "REGION=EU"]
        fbenv_layer.bespoke_frosty(
            platform=platform, format=format, config=config, frosty_args=frosty_args
        )
        expected_method_args = [platform, "EU", f"-{format}", f"-{config}", "--", "Arg1"]
        mock_fbcli_run.assert_called_once_with("frosty", method_args=expected_method_args)

    @patch("elipy2.frostbite.fbcli.run")
    def test_bespoke_frosty_args_to_remove(self, mock_fbcli_run):
        platform = "win64"
        format = "digital"
        config = "retail"
        frosty_args = ["Arg1", "IS_WIN64"]
        fbenv_layer.bespoke_frosty(
            platform=platform, format=format, config=config, frosty_args=frosty_args
        )
        expected_method_args = [platform, f"-{format}", f"-{config}", "--", "Arg1"]
        mock_fbcli_run.assert_called_once_with("frosty", method_args=expected_method_args)

    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_gensln_fbcli(self, mock_use_fbcli, mock_fbcli_run):
        mock_use_fbcli.return_value = True
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["some-args"],
            alltests=False,
            wsl=False,
            nomaster=True,
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="gensln",
            method_args=["ps4", "release", "-nomaster", "--", "some-args"],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbenv_layer.fbenv.gensln")
    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_fbenv_gensln_with_stressbulkbuild_arg(
        self, mock_use_fbcli, mock_fbcli_run, mock_fbenv_gensln
    ):
        mock_use_fbcli.return_value = False
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["some-args"],
            alltests=False,
            wsl=False,
            nomaster=True,
            stressbulkbuild=True,
        )
        mock_fbenv_gensln.assert_called_once_with(
            target="ps4",
            framework_args=["some-args"],
            nomaster=True,
            stressbulkbuild=True,
            variants=["release"],
        )

    @patch("elipy2.frostbite.fbenv_layer.fbenv.gensln")
    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_fbenv_gensln_without_stressbulkbuild_arg(
        self, mock_use_fbcli, mock_fbcli_run, mock_fbenv_gensln
    ):
        mock_use_fbcli.return_value = False
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["some-args"],
            alltests=False,
            wsl=False,
            nomaster=True,
        )
        mock_fbenv_gensln.assert_called_once_with(
            target="ps4",
            framework_args=["some-args"],
            nomaster=True,
            variants=["release"],
        )

    @patch("elipy2.frostbite.fbenv_layer.fbcli.gensln")
    @patch("elipy2.frostbite.fbenv_layer.fbenv.gensln")
    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_fbcli_gensln_with_stressbulkbuild_arg(
        self, mock_use_fbcli, mock_fbcli_run, mock_fbenv_gensln, mock_fbcli_gensln
    ):
        mock_use_fbcli.return_value = True
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["some-args"],
            alltests=False,
            wsl=False,
            nomaster=True,
            stressbulkbuild=True,
        )
        mock_fbcli_gensln.assert_called_once_with(
            target="ps4",
            framework_args=["some-args"],
            nomaster=True,
            variants=["release"],
            stressbulkbuild=True,
            alltests=False,
            wsl=False,
        )

    @patch("elipy2.frostbite.fbenv_layer.fbcli.gensln")
    @patch("elipy2.frostbite.fbenv_layer.fbenv.gensln")
    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_fbcli_gensln_without_stressbulkbuild_arg(
        self, mock_use_fbcli, mock_fbcli_run, mock_fbenv_gensln, mock_fbcli_gensln
    ):
        mock_use_fbcli.return_value = True
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["some-args"],
            alltests=False,
            wsl=False,
            nomaster=True,
        )
        mock_fbcli_gensln.assert_called_once_with(
            target="ps4",
            framework_args=["some-args"],
            nomaster=True,
            variants=["release"],
            alltests=False,
            wsl=False,
        )

    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_gensln_fbcli_with_stressbulkbuild_arg(self, mock_use_fbcli, mock_fbcli_run):
        mock_use_fbcli.return_value = True
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["some-args"],
            alltests=False,
            wsl=False,
            nomaster=True,
            stressbulkbuild=True,
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="gensln",
            method_args=["ps4", "release", "-nomaster", "-stressbulkbuild", "--", "some-args"],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_gensln_fbcli_without_stressbulkbuild_arg(self, mock_use_fbcli, mock_fbcli_run):
        mock_use_fbcli.return_value = True
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["some-args"],
            alltests=False,
            wsl=False,
            nomaster=True,
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="gensln",
            method_args=["ps4", "release", "-nomaster", "--", "some-args"],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_gensln_fbcli_with_alltests_and_minimum_fb_version(
        self, mock_use_fbcli, mock_fbcli_run
    ):
        mock_use_fbcli.return_value = True
        self.mock_minimum_fb_version.return_value = True
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["some-args"],
            alltests=True,
            wsl=False,
            nomaster=True,
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="gensln",
            method_args=["ps4", "release", "-alltests", "-nomaster", "--", "some-args"],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_gensln_fbcli_with_wsl_and_not_minimum_fb_version(self, mock_use_fbcli, mock_fbcli_run):
        mock_use_fbcli.return_value = True
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["some-args"],
            alltests=False,
            wsl=True,
            nomaster=True,
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="gensln",
            method_args=["ps4", "release", "-nomaster", "-wsl", "--", "some-args"],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_gensln_fbenv(self, mock_use_fbcli):
        mock_use_fbcli.return_value = False
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.gensln(
            target="ps4",
            framework_args=["fw_arg"],
            alltests=False,
            wsl=False,
            nomaster=True,
        )
        self.mock_gensln.assert_called_once_with(
            target="ps4",
            framework_args=["fw_arg"],
            nomaster=True,
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_gensln_fbenv_variants(self, mock_use_fbcli):
        mock_use_fbcli.return_value = False
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["fw_arg"],
            alltests=True,
            wsl=False,
            nomaster=False,
        )
        self.mock_gensln.assert_called_once_with(
            target="ps4",
            framework_args=["fw_arg"],
            nomaster=False,
            variants=["release"],
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_gensln_fbenv_with_alltests(self, mock_use_fbcli):
        mock_use_fbcli.return_value = False
        self.mock_minimum_fb_version.return_value = True
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["fw_arg"],
            alltests=True,
            wsl=False,
            nomaster=False,
        )
        self.mock_gensln.assert_called_once_with(
            target="ps4",
            framework_args=["fw_arg"],
            nomaster=False,
            variants=["release"],
            alltests=True,
            wsl=False,
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_gensln_fbenv_with_wsl(self, mock_use_fbcli):
        mock_use_fbcli.return_value = False
        self.mock_minimum_fb_version.return_value = True
        fbenv_layer.gensln(
            target="ps4",
            variants=["release"],
            framework_args=["fw_arg"],
            alltests=False,
            wsl=True,
            nomaster=False,
        )
        self.mock_gensln.assert_called_once_with(
            target="ps4",
            framework_args=["fw_arg"],
            nomaster=False,
            variants=["release"],
            alltests=False,
            wsl=True,
        )

    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_buildsln_fbcli(self, mock_use_fbcli, mock_fbcli_run):
        self.mock_minimum_fb_version.return_value = False
        mock_use_fbcli.return_value = True
        fbenv_layer.buildsln(target="ps4", config="release", msbuild_args=[])
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="buildsln",
            method_args=["ps4", "release"],
            print_std_out=True,
            capture_std_out=False,
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_buildsln_fbenv(self, mock_use_fbcli):
        mock_use_fbcli.return_value = False
        fbenv_layer.buildsln(target="ps4", config="release", msbuild_args=[])
        self.mock_buildsln.assert_called_once_with(target="ps4", config="release", msbuild_args=[])

    @patch("elipy2.frostbite.fbcli.run")
    def test_buildsln_fbenv_fail_on_first(self, mock_use_fbcli):
        mock_use_fbcli.return_value = False
        fbenv_layer.buildsln(
            target="ps4", config="release", msbuild_args=[], fail_on_first_error=True
        )
        self.mock_buildsln.assert_called_once_with(
            target="ps4", config="release", msbuild_args=[], fail_on_first_error=True
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_buildsln_fbenv_not_fail_on_first(self, mock_use_fbcli):
        mock_use_fbcli.return_value = False
        fbenv_layer.buildsln(
            target="ps4", config="release", msbuild_args=[], fail_on_first_error=False
        )
        self.mock_buildsln.assert_called_once_with(
            target="ps4", config="release", msbuild_args=[], fail_on_first_error=False
        )

    @patch("elipy2.frostbite.fbcli.run")
    def test_buildsln_fbenv_framework_args(self, mock_use_fbcli):
        mock_use_fbcli.return_value = False
        fbenv_layer.buildsln(
            target="ps4", config="release", msbuild_args=[], framework_args=["fw_arg"]
        )
        self.mock_buildsln.assert_called_once_with(
            target="ps4", config="release", msbuild_args=[], framework_args=["fw_arg"]
        )

    def test_set_datadir(self):
        fbenv_layer.set_datadir("path")
        self.mock_set_datadir.assert_called_once_with("path", store_value=True)

    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_set_datadir_fbcli(self, mock_use_fbcli, mock_fbcli_run):
        self.mock_minimum_fb_version.return_value = False
        mock_use_fbcli.return_value = True
        fbenv_layer.set_datadir("path")
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="datadir", method_args=["path"], print_std_out=True
        )

    @patch("elipy2.frostbite.fbcli.run")
    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_icepick_cook_fbcli(self, mock_use_fbcli, mock_fbcli_run):
        self.mock_minimum_fb_version.return_value = False
        mock_use_fbcli.return_value = True
        fbenv_layer.icepick_cook(
            platform="ps4",
            configuration="release",
            test_suites=["rendering", "autotest_post"],
            args=["-arg1"],
            is_local=False,
            extra_framework_args=["extra_arg1"],
            settings_files_list=[],
        )
        mock_fbcli_run.assert_called_once_with(
            fbcli_method="icepickcook",
            method_args=[
                "ps4",
                "rendering",
                "autotest_post",
                "-release",
                "--extra-framework-args=extra_arg1",
                "--",
                "-arg1",
            ],
            print_std_out=True,
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    def test_icepick_cook_fbenv_settings_not_found(self, mock_use_fbcli: MagicMock):
        mock_use_fbcli.return_value = False

        # creating the exception here because we never actually import fbenv, so we get the following error
        # when trying to test that workflow:
        #       except (TypeError, fbenv.exception.DecoratorTypeError):
        #       TypeError: catching classes that do not inherit from BaseException is not allowed
        class DecoratorTypeError(Exception):
            pass

        kwargs = {
            "platform": "ps4",
            "configuration": "release",
            "test_suites": ["rendering", "autotest_post"],
            "args": ["-arg1"],
            "is_local": False,
            "extra_framework_args": ["extra_arg1"],
            "settings_files_list": ["some_file"],
        }

        expected_kwargs = {
            "platform": "ps4",
            "configuration": "release",
            "suites": ["rendering", "autotest_post"],
            "args": ["-arg1"],
            "is_local": False,
            "extra_framework_args": ["extra_arg1"],
            "settings_files": ["some_file"],
        }

        expected_first_call = dict(expected_kwargs)
        expected_second_call = dict(expected_kwargs)
        del expected_second_call["settings_files"]

        with patch("elipy2.frostbite.fbenv_layer.fbenv", MagicMock()) as mock_fbenv:
            mock_fbenv.exception.DecoratorTypeError = DecoratorTypeError
            mock_fbenv.icepick_cook.side_effect = [TypeError(), None]

            fbenv_layer.icepick_cook(**kwargs)

            mock_fbenv.icepick_cook.assert_has_calls(
                [call(**expected_first_call), call(**expected_second_call)]
            )
            assert mock_fbenv.icepick_cook.call_count == 2

    @patch("elipy2.frostbite.fbenv_layer.fbenv")
    def test_initialize(self, mock_fbenv):
        fbenv_layer.initialize()
        mock_fbenv.initialize.assert_called_once()

    def test_get_enabled_licensees_nant(self):
        self.mock_minimum_fb_version.return_value = False
        fbenv_layer.get_enabled_licensees()
        self.mock_nant_licensees.assert_called_once_with()

    def test_get_enabled_licensees_fbenv(self):
        self.mock_minimum_fb_version.return_value = True
        fbenv_layer.get_enabled_licensees()
        self.mock_fbenv_licensees.assert_called_once_with()

    def test_set_local_root(self):
        fbenv_layer.set_local_root("local\\root")
        self.mock_set_local_root.assert_called_once_with(local_root="local\\root")

    def test_set_local_root_exception(self):
        self.mock_set_local_root.side_effect = Exception()
        with pytest.raises(FbEnvCallException):
            fbenv_layer.set_local_root("local\\root")

    def test_is_api_function_failed_exception_false(self):
        class ApiFunctionFailedException(Exception):
            pass

        with patch("elipy2.frostbite.fbenv_layer.fbenv", MagicMock()) as mock_fbenv:
            mock_fbenv.exception.ApiFunctionFailedException = ApiFunctionFailedException
            mock_fbenv.icepick_cook.side_effect = [TypeError(), None]
            assert not fbenv_layer.is_api_function_failed_exception(ELIPYException())

    def test_is_api_function_failed_exception_true(self):
        class ApiFunctionFailedException(Exception):
            pass

        with patch("elipy2.frostbite.fbenv_layer.fbenv", MagicMock()) as mock_fbenv:
            mock_fbenv.exception.ApiFunctionFailedException = ApiFunctionFailedException
            mock_fbenv.icepick_cook.side_effect = [TypeError(), None]
            assert fbenv_layer.is_api_function_failed_exception(ApiFunctionFailedException())

    @patch("elipy2.frostbite.fbenv_layer.set_environment_values")
    def test_set_environment_values_api(self, mock_set_environment_values):
        environment_variables = {
            "FB_ENV": "var1",
        }
        fbenv_layer.set_environment_values(environment_variables)
        mock_set_environment_values.assert_called_once_with(environment_variables)

    @patch("elipy2.frostbite.fbenv_layer.fbenv.update_environment_values")
    def test_set_environment_values_exception(self, mock_update_environment_values):
        mock_update_environment_values.side_effect = Exception()
        environment_variables = {
            "FB_ENV": "var1",
        }
        with pytest.raises(FbEnvCallException):
            fbenv_layer.set_environment_values(environment_variables)

    @patch("elipy2.frostbite.fbenv_layer.fbcli.install_prerequisites")
    def test_install_prerequisites_default(self, mock_install_prerequisites):
        fbenv_layer.install_prerequisites()
        mock_install_prerequisites.assert_called_once_with(buildmachine=True, noavalanche=True)

    @patch("elipy2.frostbite.fbenv_layer.fbcli.install_prerequisites")
    def test_install_prerequisites_custom(self, mock_install_prerequisites):
        fbenv_layer.install_prerequisites(buildmachine=False, noavalanche=False)
        mock_install_prerequisites.assert_called_once_with(buildmachine=False, noavalanche=False)

    @patch.dict(os.environ, {"use_fbcli": "True"}, clear=True)
    @patch("elipy2.frostbite.fbenv_layer.fbcli.install_prerequisites")
    def test_install_prerequisites_fbcli(self, mock_install_prerequisites):
        fbenv_layer.install_prerequisites()
        mock_install_prerequisites.assert_called_once_with(buildmachine=True, noavalanche=True)
