#*************************************************************
#  Sets up the initial needs to provide a vspehere instance
#*************************************************************
# Point to our provider
# https://www.terraform.io/docs/providers/vsphere/index.html
# *************************************************************
terraform {
  required_version = ">= 1.0.0"
  required_providers {
    vsphere = {
      version = "= 2.12.0" #locking Version to 2.12.0 to avoid introducing breaks
    }
    random = {
      source  = "hashicorp/random"
      version = "= 3.0.0"
    }
  }
}
provider "vsphere" {
  user           = var.vsphere_user
  password       = var.vsphere_password
  vsphere_server = var.vsphere_server
  # if you have a self-signed cert or not
  allow_unverified_ssl = true
  persist_session      = true
}

# *************************************************************
# Default Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "dice_joss_network" {
  default     = "Buildfarm (VLAN 1028)"
  description = "Name of the network name to use in datacentre"
}
variable "dice_joss_datacenter" {
  default     = "DICE"
  description = "Name of vcentre name to use e.g. DICE"
}
variable "dice_joss_compute_cluster" {
  default     = "DICE-BUILD"
  description = "Name of the compute cluster to use in datacenter"
}
variable "project_name" {
  default     = "dice-joss"
  description = "Name of the project e.g. casablanca, walrus etc"
}
variable "jenkins_slave_labels" {
  default     = "statebuild"
  description = "Labels for jenkins slave"
}
variable "disk_size" {
  default     = "700"
  description = "Size of the disk required in GB e.g. 250 = 250GB for C:"
}
variable "cpu_cores" {
  type        = string
  default     = "20"
  description = "cpu processes to be assigned to vm, ensure correct socket value is set also"
}
variable "cores_per_socket_value" {
  type        = string
  default     = "20"
  description = "cores per socket to assign, TRIPLE CHECK VALUE IS CORRECT!!"
}
variable "packer_template" {
  type        = string
  default     = "win10_22H2-cobra-v1.1101.0b76e848_PS"
  description = "Packer template for new vms. Existing vms will not be updated"
}

variable "hardware_version" {
  type        = number
  default     = 19
  description = "The hardware version for VMs in this project"
}

variable "jenkins_websocket" {
  type        = string
  description = "defines if nodes should connect to Jenkins using websockets"
  default     = "enabled"
}
# *************************************************************
# Environmental Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "vsphere_server" {}

variable "vsphere_user" {}

variable "vsphere_password" {}

variable "local_username" {}

variable "local_password" {}

variable "domain_password" {}

variable "domain_admin" {}

variable "project_dir" {}

variable "ARTIFACTORY_USER" {}

variable "ARTIFACTORY_PASSWORD" {}

variable "ARTIFACTORY_URL" {}

variable "commit_sha" {}

variable "commit_user" {}

variable "commit_url" {}
