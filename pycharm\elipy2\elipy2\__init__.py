"""
TODO: Proper docstring.
ELIPY module.
"""
from __future__ import absolute_import

import logging
import os
import sentry_sdk
from sentry_sdk import configure_scope
import six
import urllib
from elipy2.custom_logging import ELIPYLogger

try:
    import pkg_resources  # part of setuptools

    VERSION = pkg_resources.require("elipy2")[0].version
    ENV = os.environ.get("LICENSEE_ID", "local")
except Exception as _:
    VERSION = "0.0.0+UNKWOWN"
    ENV = "unknown"


def set_code_area(event):
    """
    Add tag for area of code
    """
    LOGGER.info("setting code area")
    try:
        key = "code_area"
        event["tags"] = event["tags"] or {}
        event["tags"].update({key: "elipy"})

        stack_paths = [
            frame["abs_path"].lower()
            for frame in event["exception"]["values"][0]["stacktrace"]["frames"]
        ]
        fbenv_files = [path for path in stack_paths if "bin\\fbenv\\" in path]

        if fbenv_files:
            event["tags"][key] = "fbenv"

    except Exception as _:
        LOGGER.warning("Failed to add code area")

    return event


def drop_unwanted_events(event):
    """
    Drop unwanted events.
    Events that are None are dropped
    """
    is_fbenv_code_area = event["tags"].get("code_area", "unknown") == "fbenv"
    has_drop_event_tag = event["tags"].get("SENTRY_DROP_EVENT", False)

    drop_event = any([is_fbenv_code_area, has_drop_event_tag])

    if drop_event:
        event = None

    return event


def before_send(event, hint):
    """
    Sentry before sent function
    """
    LOGGER.debug(hint)
    event = set_code_area(event)
    event = drop_unwanted_events(event)

    return event


def sentry_setup():
    """
    Setup Sentry
    """

    # Make sure we run sentry code during testing but report to a different project.
    # This removes noise from our reporting that's caused by unit tests.
    sentry_url = ""

    false_values = ["false", "0", "f", "n", "no", ""]
    enable_sentry = os.environ.get("ENABLE_SENTRY", "true").lower() not in false_values

    if os.environ.get("FB_AUTOBUILD") and enable_sentry:
        sentry_url = "https://<EMAIL>/5964113"  # pylint: disable=line-too-long

    # Bootstrap Sentry.
    sentry_sdk.init(sentry_url, release=VERSION, environment=ENV, before_send=before_send)

    # Add environment as context data to Sentry events.
    with configure_scope() as scope:
        tags = {}

        # azure tags
        if os.environ.get("BUILD_BUILDURI"):
            endpoint_url = os.environ.get(
                "SYSTEM_COLLECTIONURI", os.environ.get("ENDPOINT_URL_SYSTEMVSSCONNECTION")
            )
            team_project = os.environ.get("SYSTEM_TEAMPROJECT")
            build_id = os.environ.get("BUILD_BUILDID")
            task_id = os.environ.get("SYSTEM_TASKINSTANCEID")
            stage_id = os.environ.get("SYSTEM_STAGEID")
            job_id = os.environ.get("SYSTEM_JOBID")
            url_encoded_team_project = urllib.parse.quote(team_project)
            base_url = f"{endpoint_url}{url_encoded_team_project}/_build/results?buildId={build_id}"
            pipeline_url = f"{base_url}&view=results"
            stage_url = f"{base_url}&view=logs&s={stage_id}"
            job_url = f"{base_url}&view=logs&j={job_id}"
            task_url = f"{job_url}&t={task_id}"
            tags["PIPELINE_URL"] = pipeline_url
            tags["STAGE_URL"] = stage_url
            tags["JOB_URL"] = job_url
            tags["TASK_URL"] = task_url

        # jenkins tags
        if os.environ.get("JENKINS_URL"):
            tags["JENKINS_URL"] = os.environ.get("JENKINS_URL")
            tags["JOB_URL"] = os.environ.get("JOB_URL")
            tags["JOB_NAME"] = os.environ.get("JOB_NAME")
            tags["BUILD_URL"] = os.environ.get("BUILD_URL")

        for key, value in six.iteritems(tags):
            scope.set_tag(key, value)

        for key, value in six.iteritems(os.environ):
            scope.set_extra(key, value)


def is_dry_run():
    """
    Small helper function to allow us to dry-runs.
    """
    return True


def convert_log_level(level):
    """
    Helper function to convert str to log level
    """
    log_level_str = level.upper()
    log_levels = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }

    if log_level_str in log_levels:
        return log_levels[log_level_str]
    else:
        raise ValueError(f"Invalid log level: {log_level_str}")


def log_installation_source():
    """
    Logs whether Elipy was installed from AF2 or AF1 based on environment variables.
    This is useful for tracking the migration from AF1 to AF2.
    """
    if os.environ.get("AF2_USER") and os.environ.get("AF2_TOKEN"):
        LOGGER.info("Elipy was installed from AF2.")
    else:
        LOGGER.info("Elipy was installed from AF1.")


sentry_setup()
LOGGER = ELIPYLogger(
    __name__, convert_log_level(os.environ.get("ELIPY_LOG_LEVEL", "INFO")), None
).get_logger()
logging.root.manager.loggerDict["logger"] = LOGGER  # pylint: disable=E1103
LOGGER.debug("Logger initialized.")

from . import config  # pylint: disable=cyclic-import

SETTINGS = config.ConfigManager()

# Explicitly set this to 1337, so we can identify
# elipy-produced builds that have not configured this correctly.
if os.environ.get("MONKEY_BUILD_LABEL") is None:
    os.environ["MONKEY_BUILD_LABEL"] = "1337"


log_installation_source()
