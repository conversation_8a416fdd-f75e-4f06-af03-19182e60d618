"""
fbcli.py

Module for ELIPY's interactions with fbcli.
Allow scripts to directly invoke fbcli commands with arguments
"""

from __future__ import absolute_import
import os
from typing import List
from elipy2 import core, frostbite_core, LOGGER, SETTINGS
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics

FBCLI_PATH = os.path.join(frostbite_core.get_tnt_root(), "bin", "fbcli", "cli.bat")
FBCLI_CMD_PATH = [FBCLI_PATH, "x64", "&&", "fb"]


@collect_metrics()
def run(
    fbcli_method: str, method_args=[], print_std_out=True, capture_std_out=True, write_std_out=False
):
    """
    This funciton provide an entry to directly invoke fbcli command
    :param write_std_out:
    :param capture_std_out:
    :param fbcli_method: The fbcli method name. e.g. `icepick` , `gensln`, `pushbuild`
    :param method_args: The arguments of the command as list of strings. of strings.
    i.e. ["--attach-atftargets-log-to-test-suite", "false"]
    :param print_std_out:
    :return:
    """
    if fbcli_method is None:
        raise ValueError("fbcli_method cannot be none")
    method_args.insert(0, fbcli_method)
    cmd = FBCLI_CMD_PATH + method_args
    LOGGER.info("call {0}".format(" ".join(cmd)))
    env_patch = {"EXECUTOR": os.path.basename(__file__)}
    _allow_non_zero_exit_code = SETTINGS.get(
        "allow_fbcli_nonzero_return_code", location=SETTINGS.location, default=False
    )
    core.run(
        cmd,
        allow_non_zero_exit_code=_allow_non_zero_exit_code,
        print_std_out=print_std_out,
        capture_std_out=capture_std_out,
        write_std_out=write_std_out,
        env_patch=env_patch,
    )


def gensln(
    target=[],
    framework_args=[],
    alltests=False,
    nomaster=False,
    wsl=False,
    stressbulkbuild=False,
    variants=[],
):
    """
    Invokes fb.gensln
    :param target: Platform or Tool to generate solutions for.
    :param framework_args: Arguments passed directly to Framework.
    :param alltests: Flag to enable test generating.
    :param nomaster: Flag to disable master files generating.
    :param variants: Configuration to generate the solution.
    :return:
    """
    args = [target]
    append_fbcli_arg_condition(args, " ".join(variants), None, variants != [])
    append_fbcli_arg_condition(args, "-alltests", None, alltests is True)
    append_fbcli_arg_condition(args, "-nomaster", None, nomaster is True)
    append_fbcli_arg_condition(args, "-wsl", None, wsl is True)
    append_fbcli_arg_condition(args, "-stressbulkbuild", None, stressbulkbuild is True)

    if framework_args:
        append_fbcli_arg_condition(args, "--", None, True)
        for _framework_arg in framework_args:
            append_fbcli_arg_condition(args, _framework_arg, None, True)

    run(fbcli_method="gensln", method_args=args, print_std_out=True, capture_std_out=False)


def buildsln(target=None, config=None, msbuild_args=None, fail_on_first_error=False):
    """
    Invokes fb.buildsln
    :param target: Platform or Tool to generate solutions for.
    :param config: Configuration to build the solution.
    :param msbuild_args: The build argument to pass into msbuild
    :param fail_on_first_error: If True, msbuild will stop the build on the first error that occurs.
    :return:
    """

    args = [target]
    append_fbcli_arg_condition(args, config, None, True)
    append_fbcli_arg_condition(args, "-fail_on_first_error", None, fail_on_first_error is True)

    if msbuild_args:
        append_fbcli_arg_condition(args, "--", None, True)
        for _msbuild_arg in msbuild_args:
            append_fbcli_arg_condition(args, _msbuild_arg, None, True)

    run(fbcli_method="buildsln", method_args=args, print_std_out=True, capture_std_out=False)


def nant(
    package,
    platform,
    target,
    vsver=None,
    propertiesfile=None,
    framework_args=None,
    outsourcer=False,
    outsourcer_non_proxy_sdks=False,
    fwdwarn=False,
    fwderror=False,
    licensee_agnostic=False,
    ignoredeprecation=False,
    enableexpiredapierror=False,
    validate_package_access=False,
):
    """
    Invokes fb.nant
    :param package: Name of package in masterconfig or a path to a .build file.
    :param platform: Name of the platform(s).
    :param target: Nant target(s).
    :param vsver: Visual Studio version to generate solutions for.
    :param propertiesfile: XML file containing additional arguments to framework.
    :param framework_args: Additional arguments to framework.
    :param outsourcer: Enables outsourcing configurations.
    :param outsourcer_non_proxy_sdks: Enables outsourcing configurations by using non-proxy SDKs.
    :param fwdwarn: Forwarding headers will emit a warning.
    :param fwderror: Forwarding headers will emit an error.
    :param licensee_agnostic: Do not add licensee info to nant.exe's command line.
    :param ignoredeprecation: Frostbite deprecations are ignored and will not fail the build.
    :param enableexpiredapierror: Frostbite APIs that are expired will fail the build.
    :param validate_package_access: Validates all required packages as accessible.
    :return:
    """

    args = [package, platform, target]
    append_fbcli_arg_condition(args, "-vsver", vsver, vsver is not None)
    append_fbcli_arg_condition(args, "-propertiesfile", propertiesfile, propertiesfile is not None)
    append_fbcli_arg_condition(args, "-framework_args", framework_args, framework_args is not None)
    append_fbcli_arg_condition(args, "-outsourcer", None, outsourcer is not False)
    append_fbcli_arg_condition(
        args, "-outsourcer_non_proxy_sdks", None, outsourcer_non_proxy_sdks is not False
    )
    append_fbcli_arg_condition(args, "-fwdwarn", None, fwdwarn is not False)
    append_fbcli_arg_condition(args, "-fwderror", None, fwderror is not False)
    append_fbcli_arg_condition(args, "-licensee_agnostic", None, licensee_agnostic is not False)
    append_fbcli_arg_condition(args, "-ignoredeprecation", None, ignoredeprecation is not False)
    append_fbcli_arg_condition(
        args, "-enableexpiredapierror", None, enableexpiredapierror is not False
    )
    append_fbcli_arg_condition(
        args, "-validate_package_access", None, validate_package_access is not False
    )

    run(fbcli_method="nant", method_args=args, print_std_out=True, capture_std_out=False)


def cook(platforms=[], config=None, assets=[], pipeline_args=[], attach=False, is_local=False):
    """
    Invokes fb.cook
    :param platform: Platform to cook assets for.
    :param config: Configuration to cook assets.
    :param assets: Asset(s) to cook
    :param pipeline_args:
    :param is_local:
    :return:
    """
    args = []
    append_fbcli_arg_condition(args, "/".join(platforms), None, platforms != [])
    append_fbcli_arg_condition(args, " ".join(assets), None, assets != [])
    append_fbcli_arg_condition(args, "-localbuild", None, is_local)
    append_fbcli_arg_condition(args, "-attach", None, attach)
    append_fbcli_arg_condition(args, f"-{config}", None, config is not None)

    if pipeline_args:
        append_fbcli_arg_condition(args, "--", None, True)
        for _pipeline_arg in pipeline_args:
            append_fbcli_arg_condition(args, _pipeline_arg, None, True)

    run(fbcli_method="cook", method_args=args, print_std_out=True)


def icepick_cook(
    platform=None,
    configuration=None,
    suites=[],
    args=[],
    is_local=False,
    extra_framework_args=[],
):
    """
    Invokes fb.icepickcook
    :param platform:
    :param configuration:
    :param suites:
    :param args:
    :param is_local:
    :param extra_framework_args:
    :return:
    """
    _args = [platform]
    if suites:
        for suite in suites:
            append_fbcli_arg_condition(_args, suite, None, True)
    append_fbcli_arg_condition(_args, "-localbuild", None, is_local)
    append_fbcli_arg_condition(_args, f"-{configuration}", None, configuration is not None)
    if extra_framework_args:
        for _extra_framework_arg in extra_framework_args:
            append_fbcli_arg_condition(
                _args, "--extra-framework-args={0}".format(_extra_framework_arg), None, True
            )

    if args:
        append_fbcli_arg_condition(_args, "--", None, True)
        for arg in args:
            append_fbcli_arg_condition(_args, arg, None, True)

    run(fbcli_method="icepickcook", method_args=_args, print_std_out=True)


def pushbuild(
    label,
    artifact,
    platforms: List[str],
    config="release",
    path=None,
    subpath=None,
    eacopy_args=[],
    mirror=False,
):
    """
    Invokes fb.pushbuild
    Args:
        label: The name of the build. This is the folder the build is stored under in the
            remote path, typically a changelist number.
        artifact: The arguments of the command as list of strings. The name of the
            artifact to copy. Valid options are a licensee name or 'Test'
        platforms: The platforms to copy.
        path:  The remote path for the build. Can be an integer to indicate which of
            the default paths or a string to explicitly set a path.
            If omitted path highest path index will be used (see additional help for paths).
        subpath: A subpath to add onto the specified path that comes either from the default
             path list, or the one that is specified on the cmdline
        eacopy_args: Arguments after '--' will be passed through to EACopy.exe
    """
    args = []
    append_fbcli_arg_condition(args, "-mirror", None, mirror is True)
    args += _get_push_pull_args(label, artifact, platforms, config, path, subpath, eacopy_args)
    run("pushbuild", args)


def pullbuild(
    label,
    artifact,
    platforms: List[str],
    config="release",
    path=None,
    subpath=None,
    local=True,
    ignore_lock=True,
    mirror=False,
    eacopy_args=[],
):
    """
    Invokes fb.pullbuild
    Args:
        label: The name of the build. This is the folder the build is stored under in the
             remote path. Typically a changelist number.
        artifact: The arguments of the command as list of strings. The name of the artifact to copy.
            Valid options are a licensee name or 'Test'
        platforms: The platforms to copy.
        path:  The remote path for the build. Can be an integer to indicate which of the
            default paths or a string to explicitly set a path.
            If omitted path highest path index will be used (see additional help for paths).
        subpath: A subpath to add onto the specified path that comes either from the
             default path list, or the one that is specified on the cmdline
        local: Use local path rather than autobuild path when copying.
        ignore_lock: If set, a lock file will not be created (for pushbuild) and
            existing lock files will be ignored (for pullbuild).
            Useful for build farms where strict job ordering is already enforced
        eacopy_args: Arguments after '--' will be passed through to EACopy.exe
    """
    args = []
    append_fbcli_arg_condition(args, "-ignorelock", None, ignore_lock is True)
    append_fbcli_arg_condition(args, "-mirror", None, mirror is True)
    append_fbcli_arg_condition(args, "-local", None, local is True)
    args += _get_push_pull_args(label, artifact, platforms, config, path, subpath, eacopy_args)
    run("pullbuild", args)


def set_datadir(
    path,
    noupdatelicensee=False,
    noupdateshell=False,
    silent=False,
    switchlicensee=False,
    temporary=False,
):
    """
    Invokes fb.datadir
    :param path: Path to a valid dataset.
    :param noupdatelicensee: Don't update the licensee along with the datadir.
    :param noupdateshell: Don't update the fbcli shell variables.
    :param silent: Don't print anything to output.
    :param switchlicensee: Switch to new licensee if appropriate. (override noupdatelicensee option)
    :param temporary: Only change settings for this fbcli session.
    :return:
    """

    args = [path]
    append_fbcli_arg_condition(args, "-noupdatelicensee", None, noupdatelicensee)
    append_fbcli_arg_condition(args, "-noupdateshell", None, noupdateshell)
    append_fbcli_arg_condition(args, "-silent", None, silent)
    append_fbcli_arg_condition(args, "-switchlicensee", None, switchlicensee)
    append_fbcli_arg_condition(args, "-temporary", None, temporary)

    run(fbcli_method="datadir", method_args=args, print_std_out=True)


def set_licensee(action: str, licensee=None):
    """
    Invokes fb.licensee
    :param action: target action to manage licensee settings
    :param licensee: target licensee to operate
    :return:
    """
    acceptable_actions = ["enable", "disable", "switch", "view"]
    if action not in acceptable_actions:
        raise ELIPYException("Undefined action when running set licensee function")

    args = [action]
    if licensee:
        args.append(str(licensee))

    run(fbcli_method="licensee", method_args=args, print_std_out=True)


def install_prerequisites(buildmachine: bool = True, noavalanche: bool = True):
    """
    Invokes fb install prerequisites
    :param buildmachine: Add -buildmachine flag to the command
    :param noavalanche: Add -noavalanche flag to the command
    :return:
    """
    args = []
    append_fbcli_arg_condition(args, "-buildmachine", None, buildmachine)
    append_fbcli_arg_condition(args, "-noavalanche", None, noavalanche)

    LOGGER.info("Installing Frostbite prerequisites...")
    run(fbcli_method="install", method_args=["prerequisites"] + args, print_std_out=True)


def _get_push_pull_args(
    label, artifact, platforms: List[str], config, path, subpath, eacopy_args
) -> List[str]:
    """
    Helper function to fill in arguments for pushbuild and pulbuild. the result is stored in 'args'
    """
    ret = ["-path"]
    if path is None:
        path = SETTINGS.get("build_share", SETTINGS.location)
    _append_assert_if_none(ret, "path", path)
    _append_assert_if_none(ret, "label", label)
    _append_assert_if_none(ret, "artifact", artifact)
    if not isinstance(platforms, list):
        raise TypeError("platforms is expected to be list but got {0}".format(type(platforms)))
    if not platforms:
        raise ValueError("platforms cannot be empty")
    for platform in platforms:
        ret.append(platform)
    ret.append(config)
    append_fbcli_arg_condition(ret, "-subpath", subpath, subpath is not None)
    if eacopy_args:
        ret.append("--")
        ret += eacopy_args
    return ret


def _append_assert_if_none(args, arg_name, arg_value):
    """
    Helper function to append arg_value to args. Raise exception if arg_value is none
    """
    if arg_value is None:
        raise ValueError("{} cannot be none".format(arg_name))
    args.append(arg_value)


def append_fbcli_arg_condition(ret, arg, value, condition: bool):
    """
    Helper function add a list of string [arg , value] to ret
    if condition is true
    """
    if condition:
        if value is not None:
            ret += [arg, value]
        else:
            ret.append(arg)
