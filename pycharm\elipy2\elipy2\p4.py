# pylint: disable=too-many-lines
"""
p4.py

Module that wraps P4.exe to allow us to interact with Perforce,
but still keep control of the implementation within our team.
"""
from __future__ import absolute_import
import collections
import fnmatch
import logging
import marshal
import os
import re
import subprocess
import uuid
import json
from deprecated import deprecated
from typing import Any, Dict, List, Optional
from elipy2 import LOGGER, SETTINGS
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics, drop_telemetry_events

FilePath = collections.namedtuple("FilePath", ["local_path", "depot_path"])
ChangeInfo = collections.namedtuple("ChangeInfo", ["changelist", "user", "desc"])
StreamInfo = collections.namedtuple("StreamInfo", ["name", "stream", "parent", "type", "options"])

# pylint: disable=too-many-public-methods


class P4Utils:
    """
    Class wrapping P4 commands.
    """

    def __init__(self, port=None, user=None, client=None):
        self.port = port
        self.user = user
        self.client = client

    def _p4(self, cmd, inputfile=None) -> List[Any]:
        """
        _p4(cmd: list) -> list

        Wraps the p4 invocation and applies specific flags that we'll always pass
        Implementation on handling the output from -G is based on this:
        https://portal.perforce.com/s/article/3518
        """
        assert isinstance(cmd, (tuple, list))
        perforce_executable = "C:\\Program Files\\Perforce\\p4.exe"
        if not os.path.exists(perforce_executable):
            LOGGER.info(
                "Default path for p4 {} not found. Using checked in p4.exe from Perforce".format(
                    perforce_executable
                )
            )
            perforce_executable = "p4.exe"

        retries = SETTINGS.get("perforce_retries", default=None)
        maxwait = SETTINGS.get("perforce_maxwait", default=None)
        command = [perforce_executable, "-G"]
        if retries:
            command += ["-r{}".format(retries)]
        if maxwait:
            command += ["-vnet.maxwait={}".format(maxwait)]
        if self.port:
            command += ["-p", self.port]
        if self.user:
            command += ["-u", self.user]
        if self.client:
            command += ["-c", self.client]
        command += cmd

        if LOGGER.isEnabledFor(logging.INFO):
            LOGGER.info(subprocess.list2cmdline(command))

        with subprocess.Popen(
            command, stdin=inputfile, stdout=subprocess.PIPE, stderr=subprocess.STDOUT
        ) as perforce:
            output = []
            try:
                while True:
                    output.append(marshal.load(perforce.stdout))
            except EOFError:
                # This is "expected" once there's no more output to parse.
                pass
            except (ValueError, FileNotFoundError) as exception:
                LOGGER.error("STD out from p4 contains: {}".format(perforce.stdout.read()))
                LOGGER.error("STD err from p4 contains: {}".format(perforce.stderr))
                raise
            return output

    def set_environment(self):
        """
        Creates a P4 environment for this run on elipy.
        """
        if self.client is not None:
            os.environ["P4CLIENT"] = self.client
        if self.user is not None:
            os.environ["P4USER"] = self.user
        if self.port is not None:
            os.environ["P4PORT"] = self.port

    def shelve(self, pending_changelist, discard=False, force=True):
        """
        TODO: At the moment only the deletion part is added
        The p4 shelve command creates, modifies, or discards shelved files in a pending changelist
        https://www.perforce.com/manuals/v18.1/cmdref/Content/CmdRef/p4_shelve.html
        """
        cmd = ["shelve"]
        if discard:
            cmd += ["-d"]
        if force:
            cmd += ["-f"]
        cmd += ["-c", pending_changelist]
        result = self._p4(cmd)
        return result

    def change(self, pending_changelist, discard=False, revert=False, retry=True):
        """
        TODO: At the moment only the deletion part is added
        Create or edit a changelist specification.
        https://www.perforce.com/manuals/v18.1/cmdref/Content/CmdRef/p4_change.html
        """
        cmd = ["change"]
        if discard:
            cmd += ["-d"]
        cmd += [pending_changelist]
        result = self._p4(cmd)
        if discard:
            try:
                result = result[0].get(b"data").decode()
            except AttributeError:
                LOGGER.error("Unable to parse following message: ")
                LOGGER.error(result[0])
            else:
                not_deletable = "can't be deleted" in result

                if all([not revert, not_deletable]):
                    LOGGER.error(
                        "Couldn't delete changelist because it has open files"
                        " in it and 'revert' was not specified."
                    )
                    return None
                elif all([revert, retry, not_deletable]):
                    self.revert(pending_changelist)
                    result = self.change(pending_changelist, discard, revert, False)
                elif all([revert, not retry, not_deletable]):
                    LOGGER.error(
                        "Tried reverting files in changelist, but could still not delete it."
                    )
                    return None
        return result

    @collect_metrics()
    def unshelve(self, pending_changelist, force=True, overide_default_cl=False):
        """
        Performs an unshelve operation on a pending change.
        """
        cmd = ["unshelve"]
        if force:
            cmd += ["-f"]
        if overide_default_cl:
            cmd += ["-c", pending_changelist]
        cmd += ["-s", pending_changelist]
        result = self._p4(cmd)
        LOGGER.debug("cmd result: %s", result)

        success = True
        for item in result:
            if b"code" not in item:
                LOGGER.warning("Missing 'code' attribute in response item, skipping")
                LOGGER.warning(item)
                continue
            if b"action" in item and b"depotFile" in item:
                LOGGER.info("Unshelve %s", item[b"depotFile"])
            elif item[b"code"] == b"info":
                LOGGER.info(item[b"data"])
            elif item[b"code"] == b"error":
                LOGGER.error("Perforce error: '%s'", item[b"data"])
                success = False
            else:
                LOGGER.warning("Unknown command output: %s", item)

        if not success:
            raise ELIPYException("Failed to unshelve changelist")

        return result

    @collect_metrics()
    def revert(
        self, changelist="default", path="//...", quiet=False, wipe=True, only_unchanged=False
    ):
        """
        Performs a revert operation on all open files.
        If no changelist is specified it uses the default changelist.
        """
        if wipe and only_unchanged:
            raise ELIPYException(
                "Invalid parameter combination: 'wipe' and 'only_unchanged' both set to True."
                "This may lead to unintended behavior."
            )

        cmd = ["revert", "-c", changelist]
        if wipe:
            cmd += ["-w"]
        if only_unchanged:
            cmd += ["-a"]
        cmd += [path]

        result = self._p4(cmd)
        if len(result) == 1:
            msg = result[0].get(b"data")
            if msg:
                LOGGER.info(msg.decode().strip())
                return []

        if not quiet:
            for action in result:
                if b"depotFile" in action:
                    LOGGER.info(
                        "Reverted %s - was '%s'.",
                        action[b"depotFile"].decode(),
                        action[b"oldAction"].decode(),
                    )
                else:
                    LOGGER.debug(action)
        return result

    @collect_metrics()
    def wipe_client(self):
        """
        revert all changes and then delete pending changelists,
        keeping shelved files, unless discard_shelves is True
        """
        self.revert()
        result = self._p4(["changes", "-c", self.client, "-s", "pending"])
        for change in result:
            changelist = change[b"change"].decode()
            self.shelve(changelist, discard=True)
            self.change(pending_changelist=changelist, discard=True)

    @collect_metrics()
    def reshelve(self, shelved_changelist, target_changelist=None, force=False, promote=False):
        """
        p4 reshelve [-f][-p] -s shelved_changelist -c target_changelist

        Copies shelved files from an existing shelf into either a new shelf or
        one that has already been created. Does not require a client workspace.
        Returns new changelist if not specified in target.
        """

        LOGGER.info("Reshelving %s", shelved_changelist)
        cmd = ["reshelve"]
        if force:
            cmd += ["-f"]
        if promote:
            cmd += ["-p"]
        if target_changelist:
            cmd += ["-c", target_changelist]
        cmd += ["-s", shelved_changelist]
        result = self._p4(cmd)
        LOGGER.info(result)

        if target_changelist:
            return target_changelist
        return self.latest_pending_changelist()

    @collect_metrics()
    def latest_pending_changelist(self):
        """
        return latest pending changelist
        if no changelist, return None
        The command p4 changelists is an alias for p4 changes.
        """
        cmd = ["changelists", "-m", "1", "-s", "pending", "-c", self.client]
        result = self._p4(cmd)
        if not result:
            # "changelists -m 1 -s pending" command returns empty if
            # no changelists, this is not an error so just return None
            return None

        new_results = "".encode()
        try:
            new_results = result[0][b"change"]
        except Exception:
            LOGGER.error("Unable to parse following message:")
            LOGGER.error(result[0])
        else:
            LOGGER.info(
                "Found latest pending changelist in {0}: {1}.".format(
                    self.client, new_results.decode()
                )
            )
        return new_results.decode()

    @collect_metrics()
    def clean(self, quiet=False, folder=None):
        """
        clean(quiet: bool=False) -> list

        Performs a clean operation on the current workspace.
        """
        cmd = ["clean"]
        if folder is not None:
            cmd += [folder]

        result = self._p4(cmd)
        if len(result) == 1:
            msg = result[0].get(b"data")
            if msg:
                LOGGER.info(msg.decode().strip())
                return []

        if not quiet:
            for action in result:
                if b"action" in action:
                    try:
                        LOGGER.info(
                            "%s: Depot: %s - Client: %s",
                            action[b"action"].decode(),
                            action[b"depotFile"].decode(),
                            action[b"clientFile"].decode(),
                        )
                    except Exception:
                        LOGGER.warning("Unable to parse following message:")
                        LOGGER.warning(action)
                else:
                    LOGGER.debug(action)
        return result

    @collect_metrics()
    def resolve(self, changelist="default", quiet=False, mode=None, path=None):
        """
        Performs a resolve operation using -as unless mode is passed. The normal p4 modes are
        supported (m, f, t, y). If no changelist is specified it uses the default changelist.

        Returns a tuple (Bool, List) where bool indicates if resolve happened successfully
        (as far as we know), and list contains a list of the message objects returned from
        the server.
        """
        if mode is None:
            mode = "s"
        if mode not in ("s", "m", "f", "t", "y"):
            raise ELIPYException(f"'{mode}' is not a valid p4 resolve mode.")

        cmd = ["resolve", f"-a{mode}", "-c", changelist]
        if path:
            cmd += [path]

        result = self._p4(cmd)
        if len(result) == 1:
            msg = result[0].get(b"data")
            if msg:
                LOGGER.info(msg.decode().strip())
                return True, []

        if quiet:
            return True, result

        failed_files = []
        for action in result:
            if b"toFile" in action:
                LOGGER.info(
                    "Resolving %s -> %s as '%s'",
                    action[b"fromFile"].decode(),
                    action[b"toFile"].decode(),
                    action[b"how"].decode(),
                )
            elif b"data" in action:
                msg = action[b"data"].decode()
                lmsg = msg.lower()
                if "resolve skipped" in lmsg or "must undo move first" in lmsg:
                    failed_files.append(msg)
            else:
                LOGGER.debug(action)

        if failed_files:
            LOGGER.info("Summary of conflicting files:\n%s", "\n".join(failed_files))

        return len(failed_files) == 0, result

    def unresolved(self, mask="*", changelist="default", resolve_type=None):
        """
        Returns the list of unresolved file paths that match 'mask' and have
        specified 'resolve_type' (content, delete, branch, etc).
        """
        result = self._p4(["resolve", "-n", "-c", changelist])
        files = []

        for record in result:
            if record.get(b"code") == b"error":
                msg = record.get(b"data")
                if msg:
                    LOGGER.info(msg.decode().strip())
                return []

            local_file = record.get(b"clientFile")
            if not local_file:
                continue
            local_file = local_file.decode()
            if mask != "*" and not fnmatch.fnmatch(local_file, mask):
                continue
            depot_path = record[b"fromFile"].decode()

            if resolve_type:
                rtype = record.get(b"resolveType", b"").decode()
                if rtype != resolve_type:
                    LOGGER.info("%s - resolve type '%s', skipped", depot_path, rtype)
                    continue

            files.append(FilePath(local_file, depot_path))

        return files

    @collect_metrics()
    def integrate(
        self,
        mapping,
        stream=False,
        quiet=False,
        reverse=False,
        to_revision=None,
        parent=None,
        use_file_paths=False,
        ignore_source_history=False,
        resolve_mode=None,
    ):
        """
        Performs a p4 integrate using the given branch mapping.
        We should always do integrations with a branch mapping as a workflow standard.

        If stream is False, then mapping must be a Perforce branch mapping.
        If stream is True, then mapping must be a depot path to the stream you want to integrate
        from, and the integration will be to its parent.

        resolve_mode can be 'd', 'b', 's' or any combination of them (in a form of a string
        or a list). See the -R option for the 'p4 integrate' command.
        """
        # "-h" means do not sync the target files to the latest revisions, use what is already
        # in the workspace (i.e. 'have' revisions)
        p4_command = ["integrate", "-h"]

        if resolve_mode:
            if set(resolve_mode) - {"b", "d", "s"}:
                raise ELIPYException(f"'{resolve_mode}' is not a valid 'p4 integrate -R[x]' mode.")
            p4_command += [f"-R{''.join(resolve_mode)}"]

        if ignore_source_history:
            p4_command += ["-Di"]

        if reverse:
            p4_command += ["-r"]

        # The "-F" flag permits integration between streams with an arbitrarily specified
        # view. This is now required as of P4 server version P4D/LINUX26X86_64/2024.2/2726408
        if use_file_paths:
            if to_revision:
                p4_command += ["-F", mapping + f"@{to_revision}", parent]
            else:
                p4_command += ["-F", mapping, parent]
        else:
            if stream:
                p4_command += ["-S", mapping]
                if parent:
                    p4_command += ["-P", parent]
            else:
                p4_command += ["-F", "-b", mapping]
            if to_revision:
                p4_command += [f"@{to_revision}"]

        result = self._p4(p4_command)
        if len(result) == 1:
            msg = result[0].get(b"data")
            if msg:
                msg = msg.decode().strip()
                if "in both client and branch view" in msg:
                    raise ELIPYException(msg)

                pattern = r"^Too many rows scanned \(over \d+\); see 'p4 help maxscanrows'..*"
                if re.match(pattern, msg):
                    raise ELIPYException("P4: Too many scanned rows")

                if "Missing/wrong number of arguments" in msg:
                    raise ELIPYException(msg)

                if "File(s) not in client view" in msg:
                    raise ELIPYException(msg)

                LOGGER.info(msg)
                return []

        if not quiet:
            for action in result:
                if b"fromFile" in action:
                    LOGGER.info(
                        "Integrating %s -> %s as '%s'.",
                        action[b"fromFile"].decode(),
                        action[b"depotFile"].decode(),
                        action[b"action"].decode(),
                    )
                else:
                    if b"data" in action:
                        LOGGER.info(action[b"data"].decode())
                    else:
                        LOGGER.debug(action)
        return result

    @collect_metrics()
    def copy_mapping(
        self, mapping=None, stream=False, quiet=False, reverse=False, to_revision=None, force=False
    ):
        """
        Performs a p4 copy using the given branch mapping.

        If stream is False, then mapping must be a Perforce branch mapping.
        If stream is True, then mapping must be a depot path to the stream you want to copy
        from, and the copy will be to its parent.

        Right now there is no support for copying to non-default parent.
        """
        p4_command = ["copy"]
        if mapping and not stream and not reverse:
            p4_command += ["-F"]
        if mapping and not stream:
            p4_command += ["-b", mapping]
        if stream:
            p4_command += ["-S", mapping]
        if force:
            p4_command += ["-f"]
        if force and stream and reverse is False:
            p4_command += ["-F"]
        if reverse:
            LOGGER.info("Doing a reverse integration (parent-to-child).")
            p4_command += ["-r", "-F"]
        if to_revision:
            p4_command += ["@{0}".format(to_revision)]

        result = self._p4(p4_command)
        if len(result) == 1:
            if b"data" in result[0]:
                if b"in both client and branch view" in result[0][b"data"]:
                    raise ELIPYException(result[0][b"data"])
                if b"cannot 'copy' over outstanding" in result[0][b"data"]:
                    raise ELIPYException(result[0][b"data"])
                LOGGER.info(result[0][b"data"])
                return []

        for action in result:
            if b"fromFile" in action:
                if not quiet:
                    LOGGER.info(
                        "Integrating %s -> %s as '%s'.",
                        action[b"fromFile"].decode(),
                        action[b"depotFile"].decode(),
                        action[b"action"].decode(),
                    )
            else:
                if b"data" in action:
                    msg = action[b"data"].decode()
                    if "must sync before integrating" in msg:
                        LOGGER.error(msg)
                        raise ELIPYException(msg)
                    if not quiet:
                        LOGGER.info(msg)
                else:
                    if not quiet:
                        LOGGER.debug(action)
        return result

    def switch(self, stream: str):
        """
        Switches to a stream.
        When performing an operation on a stream, you need to be on this stream.
        This includes both submit and edit operations.

        :param stream: The stream to switch to.
        :return: The result of the switch.
        """
        result = self._p4(["switch", stream])
        LOGGER.info(result)
        return result

    @deprecated(version="17.3", reason="You should use the public method switch instead.")
    def _switch(self, stream: str):
        """
        Switches to stream.
        When doing merge-downs/copy-ups on streams
        you need to be on the stream you're about to submit to.
        """
        return self.switch(stream)

    @collect_metrics()
    def delete_workspace(self, workspace_name):
        """
        Performs a p4 client -d <workspace> on the perforce server.
        """
        p4_command = ["client", "-d", workspace_name]
        result = self._p4(p4_command)
        LOGGER.info(result)
        return result

    def stream_info(self, name=None):
        """
        Returns a StreamInfo() instance with the information about
        the specified stream (or the current stream if name=None).
        """
        p4_command = ["stream", "-o"]
        if name:
            p4_command += [name]

        result = self._p4(p4_command)[0]
        if result.get(b"code") != b"stat":
            raise ELIPYException(f"Failed to run 'p4 {' '.join(p4_command)}'")

        return StreamInfo(
            name=result[b"Name"].decode(),
            stream=result[b"Stream"].decode(),
            parent=result[b"Parent"].decode(),
            type=result[b"Type"].decode(),
            options=tuple(result[b"Options"].decode().split()),
        )

    @collect_metrics()
    def merge(self, mapping, quiet=False, reverse=True, parent=None, to_revision=None):
        """
        Performs a p4 merge using the stream relationship as defined on the perforce server.
        """
        p4_command = ["merge"]

        if reverse:
            self.switch(mapping)
            p4_command += ["-r"]

        p4_command += ["-S", mapping]
        if parent:
            p4_command += ["-P", parent]

        if to_revision:
            p4_command += [f"@{to_revision}"]

        result = self._p4(p4_command)
        if len(result) == 1:
            msg = result[0].get(b"data")
            if msg:
                msg = msg.decode().strip()
                if "in both client and branch view" in msg:
                    raise ELIPYException(msg)
                LOGGER.info(msg)
                return []

        if not quiet:
            for action in result:
                if b"fromFile" in action:
                    LOGGER.info(
                        "Integrating %s -> %s as '%s'.",
                        action[b"fromFile"].decode(),
                        action[b"depotFile"].decode(),
                        action[b"action"].decode(),
                    )
                else:
                    if b"data" in action:
                        LOGGER.info(action[b"data"].decode())
                    else:
                        LOGGER.debug(action)
        return result

    @collect_metrics()
    def copy(self, from_stream, up_to_revision=None):
        """
        Performs a P4 copy between a child stream and its parent.
        Used for copy-up jobs.

        Currently, only supports stream-based copy-ups, so would need more work to support
        branch-to-branch copying.
        """
        # Use "-f", because we want to propagate revision history about deleted files
        cmd = ["copy", "-f", "-S", from_stream]
        if up_to_revision:
            cmd += [f"@{up_to_revision}"]
        self._p4(cmd)

    @collect_metrics()
    def submit(self, message: str, revert_unchanged_files: Optional[bool] = False):
        """
        Performs a p4 submit
        """
        submit_command = ["submit", "-d", message]
        if revert_unchanged_files:
            submit_command += ["-f", "revertunchanged"]
        result = self._p4(submit_command)
        for action in result:
            submitted_cl = action.get(b"submittedChange")
            if submitted_cl:
                LOGGER.info("Submitted as CL %s", submitted_cl.decode())
                break
            msg = action.get(b"data")
            if msg and "no files to submit" in msg.decode().lower():
                LOGGER.info("No files to submit.")
                break
        else:
            LOGGER.error(result)
            raise ELIPYException("Unable to submit change. Please review the output log.")

        return result

    def reopen(self, file_name="//...", changelist="default"):
        """
        Reopens a file in changelist
        If no file is supplied all open files will be moved.
        """
        result = self._p4(["reopen", "-c", changelist, file_name])
        for action in result:
            LOGGER.info(action)

    @collect_metrics()
    def sync(self, path, to_revision=None):
        """
        Syncs files in the path, optional to add revision
        """
        if to_revision:
            path += "@{0}".format(to_revision)

        jenkins_ws = os.getenv("WORKSPACE")
        result = self._p4(["sync", path])
        for action in result:
            if jenkins_ws:
                with open(os.path.join(jenkins_ws, "logs", "p4_sync.log"), "a") as p4_log_file:
                    new_dict = {
                        k.decode("utf-8")
                        if isinstance(k, bytes)
                        else k: (v.decode("utf-8") if isinstance(v, bytes) else v)
                        for k, v in action.items()
                    }
                    # new_dict = {k.decode("utf-8"): v for k, v in action.items()}
                    p4_log_file.write(json.dumps(new_dict, indent=4))
            else:
                LOGGER.info(action)
            if b"code" in action:
                if b"error" in action[b"code"]:
                    if b"up-to-date" not in action[b"data"]:
                        raise ELIPYException(
                            f"Sync failed with: {action[b'data'].decode().strip()}"
                        )

    @collect_metrics()
    def describe(
        self, changelist: str, show_shelved_files: Optional[bool] = False
    ) -> List[Dict[Any, Any]]:
        """Runs p4 describe

        Run p4 describe
         - https://www.perforce.com/manuals/cmdref/Content/CmdRef/p4_describe.html

        Parameters
        ----------
        changelist : string
            The changelist to describe
        show_shelved_files : bool
            if true, show the shelved files

        Returns
        -------
        List[Dict[Any, Any]]
            output of all files in CL and some metadata
        """

        args = ["describe"]

        if show_shelved_files:
            args.append("-S")

        args.append(changelist)

        result = self._p4(args)

        return result

    @collect_metrics()
    def get_client(
        self,
        clientname: str,
    ) -> List[Dict[Any, Any]]:
        """Runs p4 client

        Run p4 client
         - https://www.perforce.com/manuals/cmdref/Content/CmdRef/p4_client.html

        Parameters
        ----------
        clientname : string
            Clients workspace name.

        Returns
        -------
        List[Dict[Any, Any]]
            Output client details
        """

        args = ["client", "-o", clientname]
        result = self._p4(args)

        return result

    # pylint: disable=inconsistent-return-statements
    def get_description(self, changelist="default"):
        """
        Returns the description of changelist
        """
        result = self.describe(changelist)
        for action in result:
            if b"desc" in action:
                return action[b"desc"].decode()
            if b"data" in action:
                if b"Change" in action[b"data"]:
                    description = action[b"data"].decode().split("\n")[2].strip()
                    LOGGER.info("Changelist %s has description: %s", changelist, description)
                    return description
                # Catching access denied errors
                if b"Access for user" in action[b"data"]:
                    LOGGER.info(action)
                    return ""
                if b"no such changelist." in action[b"data"]:
                    LOGGER.info(action)
                    return ""
            else:
                LOGGER.info(action)
                return ""

    def set_description(self, changelist, description):
        """
        Sets the description of changelist
        """
        result = self._p4(["change", "-o", changelist])

        for action in result:
            if b"Description" in action:
                action[b"Description"] = description.encode()

        tempfile = "{0}\\{1}".format(os.environ.get("TEMP"), str(uuid.uuid4()))

        with open(tempfile, "w+b") as _file:
            marshal.dump(result[0], _file, 0)
            _file.seek(0)
            newresult = self._p4(["change", "-i"], inputfile=_file)
            for action in newresult:
                if b"code" in action:
                    if b"error" in action[b"code"]:
                        LOGGER.error(
                            'Failed to update changelist description: "{}"'.format(
                                action[b"data"].decode()
                            )
                        )
                    elif b"info" in action[b"code"]:
                        LOGGER.info(action[b"data"].decode())

        os.remove(tempfile)

    def interchanges(self, source, target, to_revision=None, use_file_paths=False):
        """
        Returns a list of tuples with changelist, user and description of the interchanges call
        """
        p4_command = ["interchanges", "-F", "-l"]
        if use_file_paths:
            p4_command += [source, target]
        else:
            p4_command += ["-S", source, "-P", target]

        if to_revision:
            p4_command += [f"@{to_revision}"]

        result = self._p4(p4_command)
        change_list = []
        for action in result:
            if b"change" in action and b"user" in action and b"desc" in action:
                change_list.append(
                    ChangeInfo(
                        action[b"change"].decode(),
                        action[b"user"].decode(),
                        action[b"desc"].decode("utf-8", "backslashreplace"),
                    )
                )
            else:
                msg = action.get(b"data")
                if not msg:
                    LOGGER.info(action)
                    raise ELIPYException("Failed to parse interchanges output")

                LOGGER.info(msg.decode().strip())
                return []

        return change_list

    def check_for_tags(self, changelist, tags):
        """
        Checks if changelist contains any files with a tag in tags list
        """
        if isinstance(changelist, str):
            changelist = changelist.encode()
        for tag in tags:
            LOGGER.info("Checking if any files in cl %s has tag %s", changelist, tag)
            query = "//...@{}".format(tag)
            result = self._p4(["files", query])
            for item in result:
                if b"change" in item:
                    if item[b"change"] == changelist:
                        LOGGER.info(
                            "File %s is tagged with %s at %s", item[b"depotFile"], tag, changelist
                        )
                        return True
                elif item[b"code"] == b"error":
                    LOGGER.error(item[b"data"])
        LOGGER.info("No files in cl %s match tags %s", changelist, str(tags)[1:-1])
        return False

    def get_cl_by_tags(self, tags):
        """
        Find all cls with files with any of the tags
        """
        changelists = []
        for tag in tags:
            LOGGER.info("Checking for changelists with tag %s", tag)
            result = self._p4(["files", f"//...@{tag}"])
            for item in result:
                if b"change" in item:
                    LOGGER.info("File %s is tagged with %s", item[b"depotFile"], tag)
                    changelists.append(item[b"change"])
                elif item[b"code"] == b"error":
                    LOGGER.error(item[b"data"])
        changelists = list(set(changelists))
        LOGGER.info("Matching cls in list: %s", changelists)
        return changelists

    def edit(self, target):
        """
        Opens files for editing within the client workspace
        """
        result = self._p4(["edit", target])
        for action in result:
            LOGGER.info(action)
            if b"code" in action and action[b"code"] == b"error":
                raise ELIPYException("The command p4 edit failed.")

    def ignores(self):
        """
        runs the p4 ignores command
        """
        result = self._p4(["ignores"])
        for action in result:
            LOGGER.info(action)

    def p4set(self):
        """
        runs the p4 set command
        """
        result = self._p4(["set"])
        for action in result:
            LOGGER.info(action)

    def edit_integrated_files(self):
        """
        Opens files for editing within the client workspace
        """
        result = self._p4(["opened"])
        for item in result:
            if item[b"action"] == b"integrate":
                self.edit(item[b"clientFile"].decode())

    def clean_perforce(self, cleanpath):
        """
        Performs a p4 clean on either TnT or Data at game root
        """
        LOGGER.info("-------------- Performing P4 Clean -------------------")
        initial_dir = os.getcwd()
        clean_dir = os.path.join(os.environ["GAME_ROOT"], cleanpath)
        LOGGER.info("change path to %s", clean_dir)
        os.chdir(clean_dir)
        self._p4(["info", "-s"])
        ret = self._p4(["clean"])
        LOGGER.info("change path to %s", initial_dir)
        os.chdir(initial_dir)
        if ret != 0:
            raise ELIPYException("p4 clean command returned exit code {0}".format(ret))
        LOGGER.info(ret)
        LOGGER.info("-------------- successful P4 Clean -------------------)")

    def getcounter(self, countername: str):
        """
        Performs a p4 counter get on a specific counter
        """
        LOGGER.info(
            "-------------- Performing P4 get on counter %s -------------------", countername
        )

        result = self._p4(["counter", countername])
        for action in result:
            counter_value = action[b"value"].decode()
            LOGGER.info("The current value for %s is %s", countername, counter_value)
            return counter_value

    def setcounter(self, countername, value):
        """
        Sets a counter in perforce along with a given value
        """
        # Make sure we pass an actual counter value to update, otherwise fail the build
        # since this would be disastrous for the build system and anyone using drone
        # pylint: disable=line-too-long
        LOGGER.info(
            "-------------- Performing P4 set on counter %s with value %s -------------------",
            countername,
            value,
        )

        if value is None:
            raise ELIPYException("P4 counter value not passed")

        result = self._p4(["counter", countername, value])
        for action in result:
            LOGGER.info("----dump output %s ----", action)  # for debug purpose
            if action[b"value"] == str.encode(value):
                LOGGER.info("Counter {0} set successfully".format(countername))
            else:
                raise ELIPYException(
                    "P4 counter {0} not set value {1} properly".format(countername, value)
                )

    @collect_metrics()
    def reconcile(self, path, changelist=None, options=("f",), quiet=False):
        """
        Open files for add, delete, and/or edit in order to reconcile
        a workspace with changes made outside Perforce.
        """
        cmd = ["reconcile"]
        if all(flag in ["e", "a", "d", "n", "f", "I", "l"] for flag in options):
            cmd += ["-" + flag for flag in options]
        else:
            raise ELIPYException("{0} contains invalid p4 reconcile flags.".format(options))

        cmd += ["-c"]
        if changelist:
            cmd += [changelist]
        else:
            cmd += ["default"]
        cmd += [path]

        result = self._p4(cmd)
        if not quiet:
            for action in result:
                LOGGER.info(action)

        return result

    def remotes(self, spec_pattern, force_case_insensitive=True, count=1, quiet=False):
        """
        Runs p4 remotes

        Run p4 remotes passing in the specified spec_pattern. By default, it will
        limit the response to 1. Search are case-insensitive by default regardless of
        the server settings
         - https://www.perforce.com/manuals/v18.1/cmdref/Content/CmdRef/p4_remotes.html

        Parameters
        ----------
        spec_pattern : string
            The pattern used to find remote specs
        force_case_insensitive : bool
            Ignore server settings and perform case insensitive search
        count : int
            Number of items to return from the search
        quiet : bool
            Should display output from command or not

        Returns
        -------
        list(string)
            list of strings containing all remote spec names found
        """
        p4_args = ["remotes"]

        p4_args += ["-m", str(count)]

        if force_case_insensitive:
            p4_args.append("-E")
        else:
            p4_args.append("-e")

        p4_args += [spec_pattern]
        result = self._p4(p4_args)
        if not quiet:
            for output_line in result:
                LOGGER.info(output_line)

        remote_specs = []
        for output_line in result:
            spec_name = output_line[b"RemoteID"].decode("utf-8")
            remote_specs.append(spec_name)

        return remote_specs

    @collect_metrics()
    def push(self, remote_spec, dry_run=False, verbose=False, quiet=False, extra_args=None):
        """
        Runs p4 push

        Run p4 push using the specified remote spec
         - https://www.perforce.com/manuals/v18.1/cmdref/Content/CmdRef/p4_push.html

        Parameters
        ----------
        remote_spec : string
            The name of the remove spec to `push` with
        dry_run : bool
            Should the command be ran with `-n`
            Don't actually run the command, just see what it would do
        verbose : bool
            Should the command be ran with `-v`
        quiet : bool
            Should we display the command output
        extra_args : list(string)
            Any extra commands to run with

        Returns
        -------
        list(string)
            list of strings containing all output
        """

        # Construct command
        p4_args = ["push"]

        if dry_run:
            p4_args.append("-n")

        if verbose:
            p4_args.append("-v")

        p4_args += ["-r", remote_spec]
        if extra_args:
            p4_args += extra_args

        # Run command
        result = self._p4(p4_args)

        self._dvcs_errors("push", remote_spec, result, quiet)

        return result

    @collect_metrics()
    def fetch(self, remote_spec, dry_run=False, verbose=False, quiet=False, extra_args=None):
        """
        Runs p4 fetch

        Run p4 push using the specified remote spec
         - https://www.perforce.com/manuals/v18.1/cmdref/Content/CmdRef/p4_push.html

        Parameters
        ----------
        remote_spec : string
            The name of the remove spec to `push` with
        dry_run : bool
            Should the command be ran with `-n`
            Don't actually run the command, just see what it would do
        verbose : bool
            Should the command be ran with `-v`
        quiet : bool
            Should we display the command output
        extra_args : list(string)
            Any extra commands to run with

        Returns
        -------
        list(string)
            list of strings containing all output
        """

        # Construct command
        p4_args = ["fetch"]

        if dry_run:
            p4_args.append("-n")

        if verbose:
            p4_args.append("-v")

        p4_args += ["-r", remote_spec, "-k"]
        if extra_args:
            p4_args += extra_args

        # Run command
        result = self._p4(p4_args)

        self._dvcs_errors("fetch", remote_spec, result, quiet)

        return result

    @staticmethod
    def _dvcs_errors(dvcs_command, remote_spec, result, quiet=False):
        """
        Internal function to handle error parsing for the result we receive from
        the DVCS commands `p4 fetch` and `p4 push`.
        """
        success = True
        with drop_telemetry_events():
            for action in result:
                if b"code" not in action:
                    LOGGER.warning(
                        "Missing 'code' attribute in response item, skipping: %s", action
                    )
                    continue
                # Check for errors
                if action[b"code"] == b"error":
                    # Not finding new changelists is treated as an error by
                    # Perforce (but not by us).
                    # This means that we need to check the error data before failing the job.
                    if b"no revision(s) above those at that changelist number" in action[b"data"]:
                        if not quiet:
                            LOGGER.info("This is an error type we currently ignore: %s", action)
                    elif b"doesn't exist" in action[b"data"]:
                        success = False
                        LOGGER.error(
                            "Remote spec %s not found: %s",
                            remote_spec,
                            action[b"data"],
                            exc_info=True,
                        )
                    else:
                        success = False
                        LOGGER.error("unknown error: %s", action[b"data"], exc_info=True)
                elif not quiet:
                    LOGGER.info(action)

        if not success:
            LOGGER.error("Errors found whilst running dvcs, see logs for details", stack_info=True)

        if not success:
            raise ELIPYException(
                "Something went wrong during `p4 %s`. See the log for more details.", dvcs_command
            )

    @collect_metrics()
    def changes_range(self, path, changelist_start, changelist_end="#head", remove_start=True):
        """
        Returns a list of changelists later than changelist_start and
        up to changelist_end.
        """
        result = self._p4(["changes", f"{path}/...@{changelist_start},{changelist_end}"])
        changes = [int(r[b"change"].decode()) for r in result]
        if remove_start:
            start_cl = int(changelist_start)
            if start_cl in changes:
                changes.remove(start_cl)
        changes.reverse()
        return changes

    def opened(self, changelist: str = "default", path: str = "//...") -> List[str]:
        """
        Returns a list of files that are opened in the current workspace.
        :param changelist: Use a specific changelist.
        :param path: Perforce path, can be either a file or a directory.
        :return: A list of opened files.
        """
        command = ["opened", "-c", f"{changelist}", f"{path}"]
        result = self._p4(command)

        # Check if result is a list and has the expected structure
        if isinstance(result, list):
            opened_files = [r[b"depotFile"].decode() for r in result]
            return opened_files
        else:
            # Dump the result and raise an exception
            error_message = f"Unexpected result structure: {result}\nCommand: {' '.join(command)}"
            LOGGER.error(error_message)
            raise ELIPYException(f"P4 command error: {error_message}")
