"""
data.py

Module to handle data build.
"""
from __future__ import absolute_import
from builtins import object
import os
from pathlib import Path
from platform import node
import shutil
import time
from typing import List, Union
from deprecated import deprecated

from elipy2 import (
    avalanche,
    core,
    local_paths,
    LOGGER,
    SETTINGS,
    frostbite_core,
)
from elipy2.frostbite import fbenv_layer
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


# pylint: disable=too-few-public-methods
class DataUtils(object):
    """
    Utils class for data
    """

    def __init__(
        self,
        platform,
        assets,
        monkey_build_label=None,
        overwrite_p4config=True,
        use_local_tool=True,
    ):
        self.platform = platform
        self.assets = assets
        self.use_local_tool = use_local_tool
        if core.is_buildsystem_run():
            frostbite_core.set_monkey_build_label(monkey_build_label)

            if overwrite_p4config:
                core.ensure_p4_config()

    @collect_metrics()
    def cook(
        self,
        assets: Union[List, None] = None,
        pipeline_args: Union[List, None] = None,
        indexing_args: Union[List, None] = None,
        trim: bool = True,
        collect_mdmps: bool = False,
        clean_index: bool = False,
        clean_master_version_check: bool = False,
        disable_caches: bool = False,
        asset_chunk_size: int = 50,
        use_response_file: bool = False,
        skip_indexing: bool = False,
        only_indexing: bool = False,
    ) -> None:
        """
        Cook data assets.

        The param trim is handled as a special case.
        Since we want to pass -trim/-emitSuperbundles 99.99% of the time,
        we make that a default even if other custom pipeline args are passed.

        :param assets: The list of assets to cook.
        :param pipeline_args: Args used for the data build pipeline.
        :param indexing_args: Args to pass the indexing cook.
        :param trim: Include trim arg in the pipeline args.
        :param collect_mdmps: Collect minidumps and copy them to the network share.
        :param clean_index: Run the index step with a setting to clean the state.
        :param clean_master_version_check: Add pipeline args to handle clean master version check.
        :param disable_caches: Add pipeline args to disable caches.
        :param asset_chunk_size: Number of assets to cook in each chunk, if we have a large list.
        :param use_response_file: Use a response file to cook the assets.
        :param skip_indexing: Skip the indexing step.
        :param only_indexing: Only run the indexing step.
        :return: None
        :raises: ELIPYException or FbEnvCallException, from the called function fbenv_layer.cook
        """
        LOGGER.start_group("Start cooking")
        LOGGER.info("------------------- Cooking ---------------------")
        if pipeline_args is None:
            pipeline_args = []

        if indexing_args is None:
            indexing_args = []

        if trim:
            if frostbite_core.get_emit_arg() not in pipeline_args:
                pipeline_args.append(frostbite_core.get_emit_arg())

        clean_master_version_args = []
        if clean_master_version_check:
            clean_master_version_args = DataUtils.get_clean_master_version_args()

        disable_caches_args = []
        if disable_caches:
            disable_caches_args = [
                "-clean",
                "-forget",
                "-Cache.Disable",
                "true",
                "-forceBuild",
                "-trace",
            ]

        clean_index_args = ["-clean"] if clean_index else []

        if assets is None:
            assets = self.assets

        if not isinstance(assets, list):
            raise ELIPYException("Assets must be passed as a list.")

        if not skip_indexing:
            LOGGER.info("Preindexing to set up avalanche for build")
            self.run_indexing(
                self.use_local_tool,
                pipeline_args=clean_master_version_args
                + disable_caches_args
                + clean_index_args
                + indexing_args,
            )

        content_layer = self.get_content_layer(pipeline_args)
        content_layer_text = f"content layer '{content_layer}'" if content_layer else "source layer"

        if only_indexing:
            LOGGER.end_group()
            return

        LOGGER.info(
            "Cooking %s asset(s) for platform %s on %s: %s",
            len(assets),
            self.platform,
            content_layer_text,
            " ".join(assets),
        )
        LOGGER.info("pipeline_args: {0}".format(pipeline_args))
        try:
            extra_pipeline_args = pipeline_args + clean_master_version_args + disable_caches_args
            if use_response_file:
                self.response_cooking(assets=assets, pipeline_args=extra_pipeline_args)
            else:
                self.chunk_cooking(
                    assets=assets,
                    pipeline_args=extra_pipeline_args,
                    asset_chunk_size=asset_chunk_size,
                )
            LOGGER.end_group()
        except Exception:
            LOGGER.error(
                "Failed to cook data for {0} on {1} {2}".format(
                    self.platform, content_layer_text, assets
                ),
                exc_info=True,
            )
            if collect_mdmps:
                self.copy_mdmp_to_filer()
            LOGGER.end_group()
            raise

    def response_cooking(self, assets: List, pipeline_args: List) -> None:
        """
        Write assets to a response file and use that to cook. Documentation on response file
        cooking found here: https://go.ea.com/Response+Cook

        :param assets: The list of assets to cook.
        :param pipeline_args: Args used for the data build pipeline.
        :return: None
        :raises: ELIPYException or FbEnvCallException, from the called function fbenv_layer.cook
        """

        response_tnt_folder = "Local"
        response_folder_path = Path(frostbite_core.get_tnt_root(), response_tnt_folder)
        response_folder_path.mkdir(parents=True, exist_ok=True)
        # Response file must not have an extension
        response_file_name = f"{time.strftime('%Y%m%d-%H%M%S')}-response"
        response_file_path = os.path.join(response_folder_path, response_file_name)
        LOGGER.info(
            "Writing %s asset(s) to response file: %s", str(len(assets)), response_file_path
        )
        with open(response_file_path, "w") as response_file:
            for asset in assets:
                response_file.write(asset + "\n")
        fbenv_layer.cook(
            platforms=[self.platform],
            # Response file path must be relative to TnT root
            assets=f"@{Path(response_tnt_folder, response_file_name)}",
            pipeline_args=pipeline_args,
            is_local=self.use_local_tool,
        )
        # Remove the response file if cook was successful
        os.remove(response_file_path)

    def get_content_layer(self, pipeline_args=[]) -> str:
        """
        Get the content layer from the pipeline args.

        :param pipeline_args: The pipeline args to check.
        :return: The content layer, or None if not found.
        """
        for i, arg in enumerate(pipeline_args):
            if arg.lower() == "-activecontentlayer" and i + 1 < len(pipeline_args):
                return pipeline_args[i + 1]
        return None

    def chunk_cooking(self, assets: List, pipeline_args: List, asset_chunk_size: int) -> None:
        """
        Split up long lists of assets into chunks of a certain size (eg: 50).
        This way the cook command line will not become too long (8191 chars).
        https://learn.microsoft.com/en-US/troubleshoot/windows-client/shell-experience/command-line-string-limitation

        :param assets: The list of assets to cook.
        :param pipeline_args: The pipeline args to use for the cook.
        :param asset_chunk_size: The number of assets to cook in one chunk.
        :return: None
        :raises: ELIPYException or FbEnvCallException, from the called function fbenv_layer.cook
        """
        asset_chunk_generator = core.list_chunk_generator(
            large_list=assets, chunk_size=asset_chunk_size
        )
        asset_position = 0
        for asset_chunk in asset_chunk_generator:
            LOGGER.info(
                "Cooking assets: %s - %s (total number of assets: %s)",
                asset_position + 1,
                asset_position + len(asset_chunk),
                len(assets),
            )
            fbenv_layer.cook(
                platforms=[self.platform],
                assets=asset_chunk,
                pipeline_args=pipeline_args,
                is_local=self.use_local_tool,
            )
            asset_position += len(asset_chunk)

    @staticmethod
    def get_clean_master_version_args() -> List[str]:
        """
        Return args that are used when we have clean_master_version_check enabled

        :return: Returns a list of arguments that can be passed into Pipeline
        :rtype: list[str]
        """

        args = [
            "-Pipeline.EnableDatabaseMasterVersion",
            "true",
        ]

        # After this version, the argument is deprecated
        if not frostbite_core.minimum_fb_version(year=2023, season=2, version_nr=3):
            args += [
                "-Pipeline.AllowSourceDataModulePathOverrides",
                "false",
            ]

        return args

    @collect_metrics()
    def extract_expression_debugdata(self, pipeline_args=[], clean_master_version_check=False):
        """
        Extracts expression debug data from already cooked db
        """
        export_location = local_paths.get_local_expressiondebug_path()
        core.delete_folder(export_location)
        expression_debug_args = [
            "-f",
            "extractExpressionDebugData",
            "-exportDir",
            export_location,
        ]

        clean_master_version_args = []
        if clean_master_version_check:
            clean_master_version_args = DataUtils.get_clean_master_version_args()

        assets = self.assets

        LOGGER.info(
            "Extracting expression data for assets: {0} for platform: {1}".format(
                " ".join(assets), self.platform
            )
        )
        LOGGER.debug("pipeline_args: {0}".format(pipeline_args))
        try:
            pipeline_args = pipeline_args + expression_debug_args + clean_master_version_args
            fbenv_layer.cook(
                platforms=[self.platform],
                assets=assets,
                pipeline_args=pipeline_args,
                is_local=self.use_local_tool,
            )
        except Exception:
            LOGGER.error(
                "Failed to extract expression data for {0} {1}".format(self.platform, assets),
                exc_info=True,
            )
            if SETTINGS.get("project_name") != "bct":  # COBRA-3318
                raise

    @staticmethod
    def run_indexing(use_local_tool=True, pipeline_args=[]):
        """
        Run avalanche indexing through fbenv
        """
        try:
            fbenv_layer.cook(
                platforms=["index"],
                is_local=use_local_tool,
                pipeline_args=pipeline_args,
                index=True,
            )
        except Exception:
            LOGGER.info("Preindexing failed, continuing with regular build")

    @staticmethod
    def copy_mdmp_to_filer():
        """
        Utility function that grabs pipeline minidumps from %TEMP% and stores
        them on filer.
        """
        temp_path = os.environ.get("TEMP")
        dest_path = os.path.join(SETTINGS.get("build_share"), "crashdumps", "pipeline_crashdumps")
        machine_name = node()

        LOGGER.info("Checking for minidumps in {}".format(temp_path))
        for _file in os.listdir(temp_path):
            source_file = os.path.join(temp_path, _file)

            dest_file_name = "{}.{}".format(machine_name, _file)
            dest_file = os.path.join(dest_path, dest_file_name)
            if os.path.isfile(source_file) and _file.endswith(".mdmp"):
                if not os.path.exists(dest_path):
                    os.makedirs(dest_path)
                LOGGER.info("Moving {} to {}".format(source_file, dest_file))
                shutil.move(source_file, dest_file)

    def clean(
        self, platform: Union[str, None] = None, extra_pipeline_args: Union[List[str], None] = None
    ):
        """
        Does a clean of the Avalanche database for the machine.
        """
        if platform is None:
            if self.platform == "reimport":
                platform = "win64"
            else:
                platform = self.platform
        if extra_pipeline_args is None:
            extra_pipeline_args = []

        fbenv_layer.cook(
            platform,
            assets=[],
            pipeline_args=["-clean", "-updateIndex"] + extra_pipeline_args,
            attach=False,
            is_local=self.use_local_tool,
        )

    @staticmethod
    def set_datadir(data_dir):
        """
        Allows us to specify which data directory we're working out of
        (fbenv default is to assume /Data, which isn't true for DICE.)
        """
        data_dir_path = os.path.join(frostbite_core.get_game_root(), data_dir)
        LOGGER.info("Setting datadir as %s ", data_dir_path)
        fbenv_layer.set_datadir(data_dir_path)
        os.environ["GAME_DATA_DIR"] = data_dir_path
        dbid = avalanche.get_database_id()
        bid = os.getenv("fb_branch_id")

        os.environ["BRANCH_NAME"] = dbid
        os.environ["fb_default_database"] = dbid + "." + bid
        os.environ["fb_default_platform_db"] = dbid + "." + bid + ".{}"
        LOGGER.info("Updating fbenv variables.")

        fbenv_layer.initialize()

    @staticmethod
    def clean_local_datastate():
        """
        Function that deletes the Data/.state of the machine that it runs on.
        Can be used i.e. when we need to do a clean databuild.
        """
        state_local = os.path.join(frostbite_core.get_game_data_dir(), ".state")
        LOGGER.info("Deleting all files and folders in {0}".format(state_local))
        return core.delete_folder(state_local)

    @staticmethod
    def clean_ant_local():
        """
        Cleans the local ANT cache folder
        """
        ant_local_dir = local_paths.get_ant_local_dir()
        ant_local = local_paths.get_ant_local()
        LOGGER.info("deleting all files and folders in {}".format(ant_local))
        core.delete_folder(ant_local)
        # Recreate Animations.apj
        apj = os.path.join(ant_local_dir, "Animations.apj")
        if not os.path.exists(apj):
            os.makedirs(ant_local_dir, exist_ok=True)
            # Pylint doesn't detect that we only try to create the file.
            open(apj, "x").close()  # pylint: disable=consider-using-with

    @staticmethod
    def run_guid_checker(check_dir=None, guid_checker_dir=None):
        """
        Runs GUID Checker
        """
        if not guid_checker_dir:
            guid_checker_dir = os.path.join(frostbite_core.get_tnt_root(), "bin")
        if not check_dir:
            check_dir = frostbite_core.get_game_data_dir()
        LOGGER.info("Running GUID Checker on %s", check_dir)
        exe = os.path.join(guid_checker_dir, "GuidChecker.exe")
        if not os.path.isfile(exe):
            LOGGER.error("Could not find GUIDChecker.exe in %s", guid_checker_dir)
            raise ELIPYException
        cmd = [exe, check_dir]
        core.run(cmd, print_std_out=True)

    @staticmethod
    @deprecated(
        version="13.1",
        reason="You should use run_frostbite_data_upgrade (correctly spelled) instead.",
    )
    def run_frostbite_data_dupgrade(
        source_game_data_dir, dest_game_data_dir, licensee, scripts_path
    ):
        """
        Deprecated version of the call to the Frostbite Database Upgrader (FDU).
        """
        DataUtils.run_frostbite_data_upgrade(
            source_game_data_dir, dest_game_data_dir, licensee, scripts_path
        )

    @staticmethod
    def run_frostbite_data_upgrade(
        source_game_data_dir: str,
        dest_game_data_dir: str,
        licensee: str,
        scripts_path: str,
        extra_fdu_args: Union[List[str], None] = None,
    ):
        """
        Function call to the Frostbite Database Upgrader (FDU).
        """
        fdu_path = local_paths.get_fdu_folder()
        fdu_exe_path = os.path.join(fdu_path, "bin", "FrostbiteDatabaseUpgrader.exe")

        basic_fdu_args = [
            fdu_exe_path,
            "/SOURCE",
            source_game_data_dir,
            "/DEST",
            dest_game_data_dir,
            "/GAMEROOT",
            frostbite_core.get_game_root(),
            "/SCRIPTS",
            scripts_path,
            "/VERBOSE",
        ]

        if extra_fdu_args is None:
            extra_fdu_args = [
                "/FULLUPGRADE",
                "/LICENSEE",
                licensee,
                "/GENERATETYPEDB",
                "/SOURCECONTROL",
                "/NOSOURCEMODULES",
                "/WRITE",
                "/PURGE",
            ]

        core.run(basic_fdu_args + extra_fdu_args, print_std_out=True)

    @collect_metrics()
    def clear_cache(self, pipeline_args=[]):
        """
        Clear the cache before running the actual cook (where we build the data assets).
        """
        LOGGER.info("Clearing the cache.")
        pipeline_args_clean = ["-f", "clearCache"]
        fbenv_layer.cook(
            platforms=[self.platform],
            pipeline_args=pipeline_args + pipeline_args_clean,
        )
