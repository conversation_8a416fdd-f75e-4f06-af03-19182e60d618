#*************************************************************
#  Sets up the initial needs to provide a vspehere instance
#*************************************************************
# Point to our provider
# https://www.terraform.io/docs/providers/vsphere/index.html
# *************************************************************
terraform {
  required_version = ">= 1.0.0"
  required_providers {
    vsphere = {
      version = "= 2.12.0"
    }
  }
}

provider "vsphere" {
  user           = var.vsphere_user
  password       = var.vsphere_password
  vsphere_server = "eala-vcenter.la.ad.ea.com"
  # if you have a self-signed cert or not
  allow_unverified_ssl = true
  persist_session      = true
}


# *************************************************************
# Default Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "template" {
  default     = "win10_22H2-cobra-v1.1101.0b76e848"
  description = "The name of the template on the datacenter to use"
}
variable "network" {
  default     = "VLAN32 ***********/22"
  description = "Name of the network name to use in datacenter"
}
variable "datacenter" {
  default     = "Los Angeles"
  description = "The name of vcentre name to use e.g. DICE "
}
variable "project_name" {
  default     = "snowcache_troy_eala"
  description = "namer of the project e.g. casablanca, walrus ect"
}
variable "cluster_name" {
  default     = "DICE Build NFS"
  description = "Name of the compute cluster to use in datacenter"
}
variable "disk_size" {
  default     = "2000"
  description = "size of the disk required in GB e.g. 250 = 250GB for C:"
}
variable "disk_size_D" {
  default     = "1100"
  description = "size of the disk required in GB e.g. 250 = 250GB for D:"
}
variable "vsphere_location" {
  default     = "DICE/terraform-nodes/snowcache_nodes"
  description = "What folder should the VMs be placed in on vSphere"
}
variable "domain_name" {
  default     = "la.ad.ea.com"
  description = "what domain should the machine join"
}
variable "domain_ou" {
  default     = "OU=Granite,OU=EALA-Build Servers,DC=la,DC=ad,DC=ea,DC=com"
  description = "The organisational unit that machines should join"
}

variable "hardware_version" {
  type        = number
  default     = 19
  description = "The hardware version for VMs in this project"
}

variable "local_admin_group" {
  type        = string
  default     = "AD\\BCT.SECURITY.BUILD.AGENTS"
  description = "Local admin group used for ansible and jenkins authentication"
}

# *************************************************************
# Environmental Settings to use
# https://www.terraform.io/docs/configuration/variables.html
# *************************************************************
variable "vsphere_server" {}

variable "vsphere_user" {}

variable "vsphere_password" {}

variable "local_username" {}

variable "local_password" {}

variable "domain_password" {}

variable "domain_admin" {}

variable "project_dir" {}

variable "ARTIFACTORY_USER" {}

variable "ARTIFACTORY_PASSWORD" {}

variable "ARTIFACTORY_URL" {}

variable "commit_sha" {}

variable "commit_user" {}

variable "commit_url" {}
